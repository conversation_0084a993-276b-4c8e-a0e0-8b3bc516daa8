"""
Integration tests for JSON output functionality.
Tests the complete JSON output workflow from data models through output generator.
"""

import json
import pytest
from unittest.mock import patch, MagicMock
from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile
from llama_tune.core.data_models import OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult, GpuInfo
from llama_tune.reporting.output_generator import generate_output


class TestJSONOutputIntegration:
    """Integration tests for JSON output functionality."""

    @patch('llama_tune.analyzer.analyzer.get_blas_backend')
    @patch('llama_tune.analyzer.analyzer.get_gpu_info')
    @patch('llama_tune.analyzer.analyzer.get_physical_cpu_cores')
    @patch('llama_tune.analyzer.analyzer.get_total_ram_gb')
    @patch('llama_tune.analyzer.analyzer.is_numa_architecture')
    def test_json_output_with_real_system_profile(self, mock_numa, mock_ram, mock_cpu, mock_gpu, mock_blas):
        """Test JSON output with a realistic system profile from analyzer."""
        # Mock analyzer functions to return realistic data
        mock_numa.return_value = False
        mock_ram.return_value = 16.0
        mock_cpu.return_value = 8
        mock_gpu.return_value = [GpuInfo(model_name="RTX 4090", vram_gb=24.0)]
        mock_blas.return_value = "cuBLAS"
        
        # Get system profile through analyzer
        system_profile = get_system_profile()
        
        # Create other components
        model_profile = ModelProfile(
            file_path="/path/to/model.gguf",
            architecture="llama",
            layer_count=32,
            quantization_type="Q4_0"
        )
        
        benchmark_result = BenchmarkResult(
            n_gpu_layers=16,
            prompt_speed_tps=10.0,
            generation_speed_tps=20.0,
            batch_size=512,
            parallel_level=4,
            prompt_speed_tps_mean=10.5,
            prompt_speed_tps_std=0.5,
            generation_speed_tps_mean=20.2,
            generation_speed_tps_std=0.8
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=["Integration test configuration"],
            ctx_size=2048,
            sampling_parameters={"temperature": 0.8}
        )
        
        # Generate JSON output
        json_output = generate_output(config, "json")
        
        # Verify it's valid JSON
        parsed_json = json.loads(json_output)
        
        # Verify system profile data is correctly included
        assert parsed_json["system_profile"]["cpu_cores"] == 8
        assert parsed_json["system_profile"]["total_ram_gb"] == 16.0
        assert parsed_json["system_profile"]["blas_backend"] == "cuBLAS"
        assert len(parsed_json["system_profile"]["gpus"]) == 1
        assert parsed_json["system_profile"]["gpus"][0]["model_name"] == "RTX 4090"
        
        # Verify generated command is included and contains expected flags
        generated_command = parsed_json["generated_command"]
        assert "llama-server" in generated_command
        assert "--use-cublas" in generated_command  # Should be added due to cuBLAS backend
        assert "--n-gpu-layers 16" in generated_command

    @patch('llama_tune.analyzer.analyzer.subprocess.run')
    def test_json_output_with_model_profile_integration(self, mock_subprocess):
        """Test JSON output integration with model profile analysis."""
        # Mock llama-gguf output
        mock_subprocess.return_value = MagicMock(
            stdout="""
            architecture: llama
            layer_count: 32
            quantization_type: Q4_0
            """,
            stderr="",
            returncode=0
        )
        
        # Mock file existence and size
        with patch('os.path.exists', return_value=True), \
             patch('os.path.getsize', return_value=4 * 1024**3):  # 4GB file
            
            # This would normally call get_model_profile, but we'll create it directly
            # since we're focusing on the JSON output integration
            model_profile = ModelProfile(
                file_path="/path/to/test_model.gguf",
                architecture="llama",
                layer_count=32,
                quantization_type="Q4_0"
            )
            
            system_profile = SystemProfile(
                cpu_cores=4,
                total_ram_gb=8.0,
                gpus=[],
                numa_detected=False,
                blas_backend="None"
            )
            
            benchmark_result = BenchmarkResult(
                n_gpu_layers=0,  # CPU-only
                prompt_speed_tps=5.0,
                generation_speed_tps=10.0,
                batch_size=None,
                parallel_level=None
            )
            
            config = OptimalConfiguration(
                system_profile=system_profile,
                model_profile=model_profile,
                best_benchmark_result=benchmark_result,
                generated_command="",
                notes=["CPU-only configuration"],
                ctx_size=4096
            )
            
            # Generate JSON output
            json_output = generate_output(config, "json")
            parsed_json = json.loads(json_output)
            
            # Verify model profile data
            assert parsed_json["model_profile"]["file_path"] == "/path/to/test_model.gguf"
            assert parsed_json["model_profile"]["architecture"] == "llama"
            assert parsed_json["model_profile"]["layer_count"] == 32
            assert parsed_json["model_profile"]["quantization_type"] == "Q4_0"
            
            # Verify CPU-only configuration
            assert parsed_json["best_benchmark_result"]["n_gpu_layers"] == 0
            generated_command = parsed_json["generated_command"]
            assert "--n-gpu-layers 0" in generated_command
            assert "--ctx-size 4096" in generated_command

    def test_json_output_command_generation_integration(self):
        """Test that JSON output correctly integrates with command generation."""
        # Create a complex configuration to test command generation
        system_profile = SystemProfile(
            cpu_cores=16,
            total_ram_gb=64.0,
            gpus=[GpuInfo(model_name="RTX 4090", vram_gb=24.0)],
            numa_detected=True,
            blas_backend="cuBLAS"
        )
        
        model_profile = ModelProfile(
            file_path="/path/to/large_model.gguf",
            architecture="llama",
            layer_count=80,
            quantization_type="Q4_K_M"
        )
        
        benchmark_result = BenchmarkResult(
            n_gpu_layers=40,
            prompt_speed_tps=15.0,
            generation_speed_tps=30.0,
            batch_size=1024,
            parallel_level=8
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=["High-performance configuration"],
            ctx_size=8192,
            sampling_parameters={
                "temperature": 0.7,
                "top_p": 0.9,
                "repeat_penalty": 1.1
            }
        )
        
        # Generate JSON output
        json_output = generate_output(config, "json")
        parsed_json = json.loads(json_output)
        
        # Verify the generated command includes all expected parameters
        generated_command = parsed_json["generated_command"]
        
        # Basic command structure
        assert generated_command.startswith("llama-server")
        
        # System-specific flags
        assert "--threads 16" in generated_command
        assert "--numa distribute" in generated_command  # NUMA detected
        assert "--use-cublas" in generated_command  # cuBLAS backend
        
        # Model and context
        assert "--model /path/to/large_model.gguf" in generated_command
        assert "--ctx-size 8192" in generated_command
        
        # GPU configuration
        assert "--n-gpu-layers 40" in generated_command
        
        # Sampling parameters
        assert "--temperature 0.7" in generated_command
        assert "--top_p 0.9" in generated_command
        assert "--repeat_penalty 1.1" in generated_command

    def test_json_output_error_handling_integration(self):
        """Test JSON output handles edge cases and potential errors gracefully."""
        # Create configuration with edge case values
        system_profile = SystemProfile(
            cpu_cores=0,  # Could not detect CPU cores
            total_ram_gb=0.0,  # Minimal RAM
            gpus=[],  # No GPUs
            numa_detected=False,
            blas_backend="Unknown"  # Unknown BLAS backend
        )
        
        model_profile = ModelProfile(
            file_path="/path/with spaces/model file.gguf",  # Path with spaces
            architecture="unknown",
            layer_count=0,
            quantization_type="unknown"
        )
        
        benchmark_result = BenchmarkResult(
            n_gpu_layers=0,
            prompt_speed_tps=0.0,
            generation_speed_tps=0.0,
            batch_size=None,
            parallel_level=None,
            individual_results=None,
            notes=None
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=None,
            sampling_parameters=None,
            use_case=None,
            max_vram_gb=None
        )
        
        # Should not raise an exception
        json_output = generate_output(config, "json")
        parsed_json = json.loads(json_output)
        
        # Verify it handles edge cases correctly
        assert parsed_json["system_profile"]["cpu_cores"] == 0
        assert parsed_json["system_profile"]["gpus"] == []
        assert parsed_json["ctx_size"] is None
        assert parsed_json["sampling_parameters"] is None
        
        # Generated command should still be valid
        generated_command = parsed_json["generated_command"]
        assert "llama-server" in generated_command
        assert "--model /path/with spaces/model file.gguf" in generated_command

    def test_json_output_preserves_benchmark_statistics(self):
        """Test that JSON output preserves all benchmark statistical data."""
        benchmark_result = BenchmarkResult(
            n_gpu_layers=20,
            prompt_speed_tps=12.0,  # Legacy field
            generation_speed_tps=25.0,  # Legacy field
            batch_size=512,
            parallel_level=4,
            prompt_speed_tps_mean=12.5,
            prompt_speed_tps_std=1.2,
            generation_speed_tps_mean=25.8,
            generation_speed_tps_std=2.1,
            individual_results=[
                {"prompt_speed_tps": 11.0, "generation_speed_tps": 24.0},
                {"prompt_speed_tps": 13.0, "generation_speed_tps": 26.0},
                {"prompt_speed_tps": 14.0, "generation_speed_tps": 27.0}
            ],
            notes=["High variance detected", "Results stabilized after 5 runs"]
        )
        
        system_profile = SystemProfile(
            cpu_cores=8, total_ram_gb=16.0, gpus=[], numa_detected=False, blas_backend="None"
        )
        
        model_profile = ModelProfile(
            file_path="/test/model.gguf", architecture="llama", layer_count=32, quantization_type="Q4_0"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=["Statistical analysis complete"]
        )
        
        json_output = generate_output(config, "json")
        parsed_json = json.loads(json_output)
        
        # Verify all statistical data is preserved
        benchmark_data = parsed_json["best_benchmark_result"]
        assert benchmark_data["prompt_speed_tps_mean"] == 12.5
        assert benchmark_data["prompt_speed_tps_std"] == 1.2
        assert benchmark_data["generation_speed_tps_mean"] == 25.8
        assert benchmark_data["generation_speed_tps_std"] == 2.1
        assert len(benchmark_data["individual_results"]) == 3
        assert benchmark_data["notes"] == ["High variance detected", "Results stabilized after 5 runs"]
