"""
Integration tests for cache functionality with BenchmarkingEngine.

Tests the interaction between BenchmarkingEngine and CacheManager to ensure
proper cache reading and writing during benchmark operations.
"""

import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock

from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.caching.cache_manager import CacheManager
from llama_tune.core.data_models import (
    OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult, GpuInfo
)


@pytest.fixture
def temp_cache_dir():
    """Create a temporary directory for cache testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_system_profile():
    """Sample system profile for testing."""
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[GpuInfo(model_name="NVIDIA RTX 3080", vram_gb=10.0)],
        numa_detected=False,
        blas_backend="CUDA"
    )


@pytest.fixture
def sample_model_profile():
    """Sample model profile for testing."""
    return ModelProfile(
        file_path="/path/to/test_model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_0"
    )


@pytest.fixture
def sample_benchmark_result():
    """Sample benchmark result for testing."""
    return BenchmarkResult(
        n_gpu_layers=24,
        prompt_speed_tps=120.5,
        generation_speed_tps=35.8,
        batch_size=None,
        parallel_level=None,
        prompt_speed_tps_mean=120.5,
        prompt_speed_tps_std=1.5,
        generation_speed_tps_mean=35.8,
        generation_speed_tps_std=1.2,
        individual_results=None,
        notes=None
    )


@pytest.fixture
def sample_optimal_config(sample_system_profile, sample_model_profile, sample_benchmark_result):
    """Sample optimal configuration for testing."""
    return OptimalConfiguration(
        system_profile=sample_system_profile,
        model_profile=sample_model_profile,
        best_benchmark_result=sample_benchmark_result,
        generated_command="llama-server -m /path/to/test_model.gguf -ngl 24",
        notes=["Integration test configuration"],
        ctx_size=2048,
        sampling_parameters={},
        use_case="default",
        max_vram_gb=None
    )


class TestCacheIntegration:
    """Integration tests for cache functionality with BenchmarkingEngine."""

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    def test_cache_hit_skips_benchmark(self, mock_get_model_profile, mock_get_system_profile,
                                     temp_cache_dir, sample_system_profile, sample_model_profile,
                                     sample_optimal_config):
        """Test that cache hit skips benchmark execution and returns cached result."""
        # Setup mocks
        mock_get_system_profile.return_value = sample_system_profile
        mock_get_model_profile.return_value = sample_model_profile
        
        # Pre-populate cache
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        cache_key = cache_manager.generate_cache_key(
            sample_system_profile, sample_model_profile, 2048, "default", None
        )
        cache_manager.save_to_cache(cache_key, sample_optimal_config)
        
        # Create engine with caching enabled
        engine = BenchmarkingEngine(use_cache=True)
        engine.cache_manager = cache_manager
        
        # Mock the actual benchmark methods to ensure they're not called
        with patch.object(engine, '_run_gpu_offload_benchmark') as mock_gpu_benchmark:
            with patch.object(engine, '_run_throughput_benchmark') as mock_throughput_benchmark:
                
                optimal_config, all_results = engine.run_benchmark(
                    model_path="/path/to/test_model.gguf",
                    ctx_size=2048,
                    use_case="default",
                    initial_num_runs=3
                )
                
                # Verify benchmark methods were not called (cache hit)
                mock_gpu_benchmark.assert_not_called()
                mock_throughput_benchmark.assert_not_called()
                
                # Verify returned config matches cached config
                assert optimal_config.generated_command == sample_optimal_config.generated_command
                assert optimal_config.ctx_size == sample_optimal_config.ctx_size
                assert len(all_results) == 0  # No new benchmark results

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    def test_cache_miss_runs_benchmark_and_saves(self, mock_get_model_profile, mock_get_system_profile,
                                                temp_cache_dir, sample_system_profile, sample_model_profile,
                                                sample_benchmark_result):
        """Test that cache miss runs benchmark and saves result to cache."""
        # Setup mocks
        mock_get_system_profile.return_value = sample_system_profile
        mock_get_model_profile.return_value = sample_model_profile
        
        # Create engine with caching enabled
        engine = BenchmarkingEngine(use_cache=True)
        engine.cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        # Mock benchmark methods to return test results
        mock_gpu_results = [sample_benchmark_result]
        with patch.object(engine, '_run_gpu_offload_benchmark', 
                         return_value=(sample_benchmark_result, mock_gpu_results)) as mock_gpu_benchmark:
            
            optimal_config, all_results = engine.run_benchmark(
                model_path="/path/to/test_model.gguf",
                ctx_size=2048,
                use_case="default",
                initial_num_runs=3
            )
            
            # Verify benchmark was executed
            mock_gpu_benchmark.assert_called_once()
            
            # Verify result was saved to cache
            cache_key = engine.cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 2048, "default", None
            )
            cached_config = engine.cache_manager.get_from_cache(cache_key)
            assert cached_config is not None
            assert cached_config.best_benchmark_result.n_gpu_layers == sample_benchmark_result.n_gpu_layers

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    def test_cache_disabled_always_runs_benchmark(self, mock_get_model_profile, mock_get_system_profile,
                                                 temp_cache_dir, sample_system_profile, sample_model_profile,
                                                 sample_benchmark_result, sample_optimal_config):
        """Test that disabled cache always runs benchmark even if cache entry exists."""
        # Setup mocks
        mock_get_system_profile.return_value = sample_system_profile
        mock_get_model_profile.return_value = sample_model_profile
        
        # Pre-populate cache
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        cache_key = cache_manager.generate_cache_key(
            sample_system_profile, sample_model_profile, 2048, "default", None
        )
        cache_manager.save_to_cache(cache_key, sample_optimal_config)
        
        # Create engine with caching disabled
        engine = BenchmarkingEngine(use_cache=False)
        
        # Mock benchmark methods
        mock_gpu_results = [sample_benchmark_result]
        with patch.object(engine, '_run_gpu_offload_benchmark',
                         return_value=(sample_benchmark_result, mock_gpu_results)) as mock_gpu_benchmark:
            
            optimal_config, all_results = engine.run_benchmark(
                model_path="/path/to/test_model.gguf",
                ctx_size=2048,
                use_case="default",
                initial_num_runs=3
            )
            
            # Verify benchmark was executed despite cache entry existing
            mock_gpu_benchmark.assert_called_once()
            assert len(all_results) > 0  # New benchmark results were generated

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    def test_cache_key_varies_with_parameters(self, mock_get_model_profile, mock_get_system_profile,
                                            temp_cache_dir, sample_system_profile, sample_model_profile,
                                            sample_benchmark_result):
        """Test that different benchmark parameters produce different cache keys."""
        # Setup mocks
        mock_get_system_profile.return_value = sample_system_profile
        mock_get_model_profile.return_value = sample_model_profile
        
        # Create engine with caching enabled
        engine = BenchmarkingEngine(use_cache=True)
        engine.cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        # Mock benchmark methods
        mock_gpu_results = [sample_benchmark_result]
        with patch.object(engine, '_run_gpu_offload_benchmark',
                         return_value=(sample_benchmark_result, mock_gpu_results)):
            
            # Run benchmark with first set of parameters
            engine.run_benchmark(
                model_path="/path/to/test_model.gguf",
                ctx_size=2048,
                use_case="default",
                initial_num_runs=3
            )
            
            # Run benchmark with different parameters
            engine.run_benchmark(
                model_path="/path/to/test_model.gguf",
                ctx_size=4096,  # Different ctx_size
                use_case="default",
                initial_num_runs=3
            )
            
            # Verify different cache keys were generated
            key1 = engine.cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 2048, "default", None
            )
            key2 = engine.cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "default", None
            )
            assert key1 != key2
            
            # Verify both results are cached separately
            cached_config1 = engine.cache_manager.get_from_cache(key1)
            cached_config2 = engine.cache_manager.get_from_cache(key2)
            assert cached_config1 is not None
            assert cached_config2 is not None
            assert cached_config1.ctx_size != cached_config2.ctx_size

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    def test_cache_with_max_vram_constraint(self, mock_get_model_profile, mock_get_system_profile,
                                          temp_cache_dir, sample_system_profile, sample_model_profile,
                                          sample_benchmark_result):
        """Test that max_vram_gb constraint affects cache key generation."""
        # Setup mocks
        mock_get_system_profile.return_value = sample_system_profile
        mock_get_model_profile.return_value = sample_model_profile
        
        # Create engine with caching enabled
        engine = BenchmarkingEngine(use_cache=True)
        engine.cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        # Mock benchmark methods
        mock_gpu_results = [sample_benchmark_result]
        with patch.object(engine, '_run_gpu_offload_benchmark',
                         return_value=(sample_benchmark_result, mock_gpu_results)):
            
            # Run benchmark without max_vram constraint
            engine.run_benchmark(
                model_path="/path/to/test_model.gguf",
                ctx_size=2048,
                use_case="default",
                initial_num_runs=3,
                max_vram_gb=None
            )
            
            # Run benchmark with max_vram constraint
            engine.run_benchmark(
                model_path="/path/to/test_model.gguf",
                ctx_size=2048,
                use_case="default",
                initial_num_runs=3,
                max_vram_gb=8.0
            )
            
            # Verify different cache keys were generated
            key1 = engine.cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 2048, "default", None
            )
            key2 = engine.cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 2048, "default", 8.0
            )
            assert key1 != key2
