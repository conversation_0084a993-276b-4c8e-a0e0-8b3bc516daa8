import pytest
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import BenchmarkResult

class TestConcurrencyBenchmark:
    @pytest.fixture
    def benchmarking_engine(self):
        return BenchmarkingEngine()

    def test_analyze_concurrency_results_empty(self, benchmarking_engine):
        results = []
        optimal_parallel_level = benchmarking_engine._analyze_concurrency_results(results)
        assert optimal_parallel_level == 0

    def test_analyze_concurrency_results_single_result(self, benchmarking_engine):
        results = [
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=10.0, generation_speed_tps=50.0, batch_size=None, parallel_level=1)
        ]
        optimal_parallel_level = benchmarking_engine._analyze_concurrency_results(results)
        assert optimal_parallel_level == 1

    def test_analyze_concurrency_results_multiple_results(self, benchmarking_engine):
        results = [
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=10.0, generation_speed_tps=50.0, batch_size=None, parallel_level=1),
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=12.0, generation_speed_tps=70.0, batch_size=None, parallel_level=2),
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=8.0, generation_speed_tps=40.0, batch_size=None, parallel_level=4)
        ]
        optimal_parallel_level = benchmarking_engine._analyze_concurrency_results(results)
        assert optimal_parallel_level == 2

    def test_analyze_concurrency_results_same_throughput_chooses_first(self, benchmarking_engine):
        results = [
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=10.0, generation_speed_tps=50.0, batch_size=None, parallel_level=1),
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=12.0, generation_speed_tps=50.0, batch_size=None, parallel_level=2) # Same throughput as first
        ]
        optimal_parallel_level = benchmarking_engine._analyze_concurrency_results(results)
        assert optimal_parallel_level == 1 # Should pick the first one encountered with max throughput

    def test_analyze_concurrency_results_negative_throughput(self, benchmarking_engine):
        results = [
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=10.0, generation_speed_tps=-5.0, batch_size=None, parallel_level=1),
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=12.0, generation_speed_tps=-10.0, batch_size=None, parallel_level=2)
        ]
        optimal_parallel_level = benchmarking_engine._analyze_concurrency_results(results)
        assert optimal_parallel_level == 1 # Should still pick the one with the highest (least negative) throughput

    def test_analyze_concurrency_results_zero_throughput(self, benchmarking_engine):
        results = [
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=10.0, generation_speed_tps=0.0, batch_size=None, parallel_level=1),
            BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=12.0, generation_speed_tps=0.0, batch_size=None, parallel_level=2)
        ]
        optimal_parallel_level = benchmarking_engine._analyze_concurrency_results(results)
        assert optimal_parallel_level == 1 # Should pick the first one encountered with max throughput (0 in this case)
