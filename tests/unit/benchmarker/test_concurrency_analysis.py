import pytest
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import BenchmarkResult

class TestConcurrencyAnalysis:
    """
    Unit tests for the _analyze_throughput_results method in BenchmarkingEngine.
    """

    @pytest.fixture
    def engine(self):
        return BenchmarkingEngine()

    def test_analyze_throughput_results_empty_list(self, engine):
        """
        Test with an empty list of results.
        Should return a default BenchmarkResult.
        """
        results = []
        n_gpu_layers = 10
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result.n_gpu_layers == n_gpu_layers
        assert optimal_result.prompt_speed_tps == 0.0
        assert optimal_result.generation_speed_tps == 0.0
        assert optimal_result.batch_size is None
        assert optimal_result.parallel_level is None

    def test_analyze_throughput_results_single_result(self, engine):
        """
        Test with a single result in the list.
        Should return that single result.
        """
        result = BenchmarkResult(
            n_gpu_layers=5,
            prompt_speed_tps=100.0,
            generation_speed_tps=50.0,
            batch_size=8,
            parallel_level=4
        )
        results = [result]
        n_gpu_layers = 5
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result == result

    def test_analyze_throughput_results_multiple_results_best_first(self, engine):
        """
        Test with multiple results where the first one is the best.
        """
        result1 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=100.0, generation_speed_tps=70.0, batch_size=8, parallel_level=4)
        result2 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=90.0, generation_speed_tps=60.0, batch_size=16, parallel_level=2)
        result3 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=110.0, generation_speed_tps=65.0, batch_size=4, parallel_level=8)
        results = [result1, result2, result3]
        n_gpu_layers = 5
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result == result1

    def test_analyze_throughput_results_multiple_results_best_last(self, engine):
        """
        Test with multiple results where the last one is the best.
        """
        result1 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=100.0, generation_speed_tps=60.0, batch_size=8, parallel_level=4)
        result2 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=90.0, generation_speed_tps=65.0, batch_size=16, parallel_level=2)
        result3 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=110.0, generation_speed_tps=70.0, batch_size=4, parallel_level=8)
        results = [result1, result2, result3]
        n_gpu_layers = 5
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result == result3

    def test_analyze_throughput_results_multiple_results_best_middle(self, engine):
        """
        Test with multiple results where the best is determined by composite performance score.
        Composite scores: result1=72.0, result2=79.5, result3=82.0 (highest)
        """
        result1 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=100.0, generation_speed_tps=60.0, batch_size=8, parallel_level=4)
        result2 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=90.0, generation_speed_tps=75.0, batch_size=16, parallel_level=2)
        result3 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=110.0, generation_speed_tps=70.0, batch_size=4, parallel_level=8)
        results = [result1, result2, result3]
        n_gpu_layers = 5
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result == result3  # result3 has the highest composite score

    def test_analyze_throughput_results_with_zero_throughput(self, engine):
        """
        Test with results including zero throughput.
        Composite scores: result1=30.0, result2=34.0, result3=36.5 (highest)
        """
        result1 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=100.0, generation_speed_tps=0.0, batch_size=8, parallel_level=4)
        result2 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=90.0, generation_speed_tps=10.0, batch_size=16, parallel_level=2)
        result3 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=110.0, generation_speed_tps=5.0, batch_size=4, parallel_level=8)
        results = [result1, result2, result3]
        n_gpu_layers = 5
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result == result3  # result3 has the highest composite score

    def test_analyze_throughput_results_all_zero_throughput(self, engine):
        """
        Test with all results having zero throughput.
        Should return the first result.
        """
        result1 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=100.0, generation_speed_tps=0.0, batch_size=8, parallel_level=4)
        result2 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=90.0, generation_speed_tps=0.0, batch_size=16, parallel_level=2)
        results = [result1, result2]
        n_gpu_layers = 5
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result == result1 # Should return the first one if all are equal (or 0)

    def test_analyze_throughput_results_tie_in_throughput(self, engine):
        """
        Test with a tie in throughput.
        Should return the first one encountered with the highest throughput.
        """
        result1 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=100.0, generation_speed_tps=70.0, batch_size=8, parallel_level=4)
        result2 = BenchmarkResult(n_gpu_layers=5, prompt_speed_tps=90.0, generation_speed_tps=70.0, batch_size=16, parallel_level=2)
        results = [result1, result2]
        n_gpu_layers = 5
        optimal_result = engine._analyze_throughput_results(results, n_gpu_layers)

        assert optimal_result == result1