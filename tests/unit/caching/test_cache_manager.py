"""
Unit tests for CacheManager.

Tests cover save_to_cache, get_from_cache, and generate_cache_key methods
with comprehensive coverage of edge cases and error conditions.
"""

import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, mock_open, MagicMock

from llama_tune.caching.cache_manager import CacheManager
from llama_tune.core.data_models import (
    OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult, GpuInfo
)


@pytest.fixture
def temp_cache_dir():
    """Create a temporary directory for cache testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_gpu_info():
    """Sample GPU info for testing."""
    return GpuInfo(model_name="NVIDIA RTX 4090", vram_gb=24.0)


@pytest.fixture
def sample_system_profile(sample_gpu_info):
    """Sample system profile for testing."""
    return SystemProfile(
        cpu_cores=16,
        total_ram_gb=32.0,
        gpus=[sample_gpu_info],
        numa_detected=False,
        blas_backend="CUDA"
    )


@pytest.fixture
def sample_model_profile():
    """Sample model profile for testing."""
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_0"
    )


@pytest.fixture
def sample_benchmark_result():
    """Sample benchmark result for testing."""
    return BenchmarkResult(
        n_gpu_layers=28,
        prompt_speed_tps=150.5,
        generation_speed_tps=45.2,
        batch_size=None,
        parallel_level=None,
        prompt_speed_tps_mean=150.5,
        prompt_speed_tps_std=2.1,
        generation_speed_tps_mean=45.2,
        generation_speed_tps_std=1.8,
        individual_results=None,
        notes=None
    )


@pytest.fixture
def sample_optimal_config(sample_system_profile, sample_model_profile, sample_benchmark_result):
    """Sample optimal configuration for testing."""
    return OptimalConfiguration(
        system_profile=sample_system_profile,
        model_profile=sample_model_profile,
        best_benchmark_result=sample_benchmark_result,
        generated_command="llama-server -m /path/to/model.gguf -ngl 28",
        notes=["Test configuration"],
        ctx_size=4096,
        sampling_parameters={"temperature": 0.7},
        use_case="default",
        max_vram_gb=None
    )


class TestCacheManager:
    """Test cases for CacheManager class."""

    def test_init_default_cache_dir(self):
        """Test CacheManager initialization with default cache directory."""
        cache_manager = CacheManager()
        assert cache_manager.cache_dir.name == "llama-tune"
        assert cache_manager.cache_dir.exists()

    def test_init_custom_cache_dir(self, temp_cache_dir):
        """Test CacheManager initialization with custom cache directory."""
        custom_dir = temp_cache_dir / "custom_cache"
        cache_manager = CacheManager(cache_dir=custom_dir)
        assert cache_manager.cache_dir == custom_dir
        assert cache_manager.cache_dir.exists()

    def test_generate_cache_key_deterministic(self, sample_system_profile, sample_model_profile):
        """Test that cache key generation is deterministic."""
        cache_manager = CacheManager()
        
        with patch.object(cache_manager, '_get_file_hash', return_value="test_hash"):
            key1 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "default"
            )
            key2 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "default"
            )
            assert key1 == key2

    def test_generate_cache_key_different_inputs(self, sample_system_profile, sample_model_profile):
        """Test that different inputs produce different cache keys."""
        cache_manager = CacheManager()
        
        with patch.object(cache_manager, '_get_file_hash', return_value="test_hash"):
            key1 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "default"
            )
            key2 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 2048, "default"  # Different ctx_size
            )
            assert key1 != key2

    def test_generate_cache_key_gpu_ordering(self, sample_model_profile):
        """Test that GPU ordering doesn't affect cache key (should be sorted)."""
        cache_manager = CacheManager()
        
        gpu1 = GpuInfo(model_name="NVIDIA RTX 4090", vram_gb=24.0)
        gpu2 = GpuInfo(model_name="NVIDIA RTX 3080", vram_gb=10.0)
        
        system1 = SystemProfile(
            cpu_cores=16, total_ram_gb=32.0, gpus=[gpu1, gpu2],
            numa_detected=False, blas_backend="CUDA"
        )
        system2 = SystemProfile(
            cpu_cores=16, total_ram_gb=32.0, gpus=[gpu2, gpu1],  # Different order
            numa_detected=False, blas_backend="CUDA"
        )
        
        with patch.object(cache_manager, '_get_file_hash', return_value="test_hash"):
            key1 = cache_manager.generate_cache_key(system1, sample_model_profile, 4096, "default")
            key2 = cache_manager.generate_cache_key(system2, sample_model_profile, 4096, "default")
            assert key1 == key2

    def test_save_to_cache_success(self, temp_cache_dir, sample_optimal_config):
        """Test successful cache saving."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        cache_key = "test_key_123"
        
        cache_manager.save_to_cache(cache_key, sample_optimal_config)
        
        cache_file = temp_cache_dir / f"{cache_key}.json"
        assert cache_file.exists()
        
        # Verify content
        with open(cache_file, 'r') as f:
            data = json.load(f)
        assert data['generated_command'] == sample_optimal_config.generated_command
        assert data['ctx_size'] == sample_optimal_config.ctx_size

    def test_save_to_cache_atomic_operation(self, temp_cache_dir, sample_optimal_config):
        """Test that cache saving is atomic (uses temporary file)."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        cache_key = "test_key_atomic"
        
        # Mock open to fail on the rename operation
        with patch('builtins.open', mock_open()) as mock_file:
            with patch('pathlib.Path.rename', side_effect=OSError("Rename failed")):
                cache_manager.save_to_cache(cache_key, sample_optimal_config)
        
        # Verify temporary file was attempted to be created
        mock_file.assert_called()

    def test_get_from_cache_success(self, temp_cache_dir, sample_optimal_config):
        """Test successful cache retrieval."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        cache_key = "test_key_get"
        
        # First save to cache
        cache_manager.save_to_cache(cache_key, sample_optimal_config)
        
        # Then retrieve
        retrieved_config = cache_manager.get_from_cache(cache_key)
        
        assert retrieved_config is not None
        assert retrieved_config.generated_command == sample_optimal_config.generated_command
        assert retrieved_config.ctx_size == sample_optimal_config.ctx_size
        assert retrieved_config.system_profile.cpu_cores == sample_optimal_config.system_profile.cpu_cores

    def test_get_from_cache_miss(self, temp_cache_dir):
        """Test cache miss (key not found)."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        result = cache_manager.get_from_cache("nonexistent_key")
        assert result is None

    def test_get_from_cache_corrupted_file(self, temp_cache_dir):
        """Test handling of corrupted cache file."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        cache_key = "corrupted_key"
        
        # Create a corrupted cache file
        cache_file = temp_cache_dir / f"{cache_key}.json"
        with open(cache_file, 'w') as f:
            f.write("invalid json content")
        
        result = cache_manager.get_from_cache(cache_key)
        assert result is None
        # Corrupted file should be removed
        assert not cache_file.exists()

    def test_get_file_hash_success(self, temp_cache_dir):
        """Test successful file hashing."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        # Create a test file
        test_file = temp_cache_dir / "test_file.txt"
        test_content = "test content for hashing"
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        hash_result = cache_manager._get_file_hash(str(test_file))
        assert isinstance(hash_result, str)
        assert len(hash_result) == 64  # SHA256 hex length

    def test_get_file_hash_file_not_found(self, temp_cache_dir):
        """Test file hashing with non-existent file."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        hash_result = cache_manager._get_file_hash("/nonexistent/file.txt")
        assert isinstance(hash_result, str)
        assert len(hash_result) == 64  # Should return fallback hash

    def test_get_tool_version(self, temp_cache_dir):
        """Test tool version retrieval."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        version = cache_manager._get_tool_version()
        assert isinstance(version, str)
        assert len(version) > 0

    def test_serialize_deserialize_roundtrip(self, temp_cache_dir, sample_optimal_config):
        """Test that serialization and deserialization preserve data integrity."""
        cache_manager = CacheManager(cache_dir=temp_cache_dir)
        
        # Serialize
        serialized = cache_manager._serialize_optimal_configuration(sample_optimal_config)
        
        # Deserialize
        deserialized = cache_manager._deserialize_optimal_configuration(serialized)
        
        # Compare key fields
        assert deserialized.generated_command == sample_optimal_config.generated_command
        assert deserialized.ctx_size == sample_optimal_config.ctx_size
        assert deserialized.use_case == sample_optimal_config.use_case
        assert deserialized.system_profile.cpu_cores == sample_optimal_config.system_profile.cpu_cores
        assert len(deserialized.system_profile.gpus) == len(sample_optimal_config.system_profile.gpus)

    def test_cache_key_includes_max_vram_gb(self, sample_system_profile, sample_model_profile):
        """Test that max_vram_gb parameter affects cache key."""
        cache_manager = CacheManager()
        
        with patch.object(cache_manager, '_get_file_hash', return_value="test_hash"):
            key1 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "default", max_vram_gb=None
            )
            key2 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "default", max_vram_gb=16.0
            )
            assert key1 != key2

    def test_cache_key_includes_use_case(self, sample_system_profile, sample_model_profile):
        """Test that use_case parameter affects cache key."""
        cache_manager = CacheManager()
        
        with patch.object(cache_manager, '_get_file_hash', return_value="test_hash"):
            key1 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "default"
            )
            key2 = cache_manager.generate_cache_key(
                sample_system_profile, sample_model_profile, 4096, "multi-user-server"
            )
            assert key1 != key2
