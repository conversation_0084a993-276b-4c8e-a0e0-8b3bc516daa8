"""
Unit tests for JSON output functionality.
Tests the JSON serialization of OptimalConfiguration objects as required by Story 4.3.
"""

import json
import pytest
from llama_tune.reporting.output_generator import generate_output
from llama_tune.core.data_models import OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult, GpuInfo


@pytest.fixture
def sample_gpu_info():
    """Sample GPU information for testing."""
    return GpuInfo(model_name="RTX 4090", vram_gb=24.0)


@pytest.fixture
def sample_system_profile(sample_gpu_info):
    """Sample system profile for testing."""
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[sample_gpu_info],
        numa_detected=False,
        blas_backend="cuBLAS"
    )


@pytest.fixture
def sample_model_profile():
    """Sample model profile for testing."""
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_0"
    )


@pytest.fixture
def sample_benchmark_result():
    """Sample benchmark result for testing."""
    return BenchmarkResult(
        n_gpu_layers=16,
        prompt_speed_tps=10.0,
        generation_speed_tps=20.0,
        batch_size=512,
        parallel_level=4,
        prompt_speed_tps_mean=10.5,
        prompt_speed_tps_std=0.5,
        generation_speed_tps_mean=20.2,
        generation_speed_tps_std=0.8,
        individual_results=[
            {"prompt_speed_tps": 10.0, "generation_speed_tps": 20.0},
            {"prompt_speed_tps": 11.0, "generation_speed_tps": 20.4}
        ],
        notes=["Benchmark completed successfully"]
    )


@pytest.fixture
def sample_optimal_configuration(sample_system_profile, sample_model_profile, sample_benchmark_result):
    """Sample optimal configuration for testing."""
    return OptimalConfiguration(
        system_profile=sample_system_profile,
        model_profile=sample_model_profile,
        best_benchmark_result=sample_benchmark_result,
        generated_command="",  # Will be populated by output generator
        notes=["Configuration optimized for performance"],
        ctx_size=2048,
        sampling_parameters={"temperature": 0.8, "top_p": 0.95},
        use_case="default",
        max_vram_gb=20.0
    )


class TestJSONOutput:
    """Test class for JSON output functionality."""

    def test_json_output_is_valid_json(self, sample_optimal_configuration):
        """Test that JSON output produces valid JSON."""
        output = generate_output(sample_optimal_configuration, "json")
        
        # Should be able to parse as JSON without errors
        parsed_json = json.loads(output)
        assert isinstance(parsed_json, dict)

    def test_json_output_contains_all_required_fields(self, sample_optimal_configuration):
        """Test that JSON output contains all required fields from the story."""
        output = generate_output(sample_optimal_configuration, "json")
        parsed_json = json.loads(output)
        
        # AC 2: JSON object contains all detected system information, model metadata, 
        # benchmark results, and the final generated llama-server command
        
        # System information
        assert "system_profile" in parsed_json
        assert "cpu_cores" in parsed_json["system_profile"]
        assert "total_ram_gb" in parsed_json["system_profile"]
        assert "gpus" in parsed_json["system_profile"]
        assert "numa_detected" in parsed_json["system_profile"]
        assert "blas_backend" in parsed_json["system_profile"]
        
        # Model metadata
        assert "model_profile" in parsed_json
        assert "file_path" in parsed_json["model_profile"]
        assert "architecture" in parsed_json["model_profile"]
        assert "layer_count" in parsed_json["model_profile"]
        assert "quantization_type" in parsed_json["model_profile"]
        
        # Benchmark results
        assert "best_benchmark_result" in parsed_json
        assert "n_gpu_layers" in parsed_json["best_benchmark_result"]
        assert "prompt_speed_tps" in parsed_json["best_benchmark_result"]
        assert "generation_speed_tps" in parsed_json["best_benchmark_result"]
        
        # Generated command
        assert "generated_command" in parsed_json
        assert isinstance(parsed_json["generated_command"], str)
        assert len(parsed_json["generated_command"]) > 0  # Should not be empty

    def test_json_output_includes_generated_command(self, sample_optimal_configuration):
        """Test that JSON output includes the actual generated command."""
        output = generate_output(sample_optimal_configuration, "json")
        parsed_json = json.loads(output)
        
        generated_command = parsed_json["generated_command"]
        
        # Should contain llama-server command
        assert "llama-server" in generated_command
        assert "--model /path/to/model.gguf" in generated_command
        assert "--ctx-size 2048" in generated_command
        assert "--n-gpu-layers 16" in generated_command

    def test_json_output_preserves_data_types(self, sample_optimal_configuration):
        """Test that JSON output preserves correct data types."""
        output = generate_output(sample_optimal_configuration, "json")
        parsed_json = json.loads(output)
        
        # Check integer types
        assert isinstance(parsed_json["system_profile"]["cpu_cores"], int)
        assert isinstance(parsed_json["model_profile"]["layer_count"], int)
        assert isinstance(parsed_json["best_benchmark_result"]["n_gpu_layers"], int)
        
        # Check float types
        assert isinstance(parsed_json["system_profile"]["total_ram_gb"], float)
        assert isinstance(parsed_json["best_benchmark_result"]["prompt_speed_tps"], float)
        assert isinstance(parsed_json["best_benchmark_result"]["generation_speed_tps"], float)
        
        # Check boolean types
        assert isinstance(parsed_json["system_profile"]["numa_detected"], bool)
        
        # Check string types
        assert isinstance(parsed_json["system_profile"]["blas_backend"], str)
        assert isinstance(parsed_json["model_profile"]["architecture"], str)
        assert isinstance(parsed_json["generated_command"], str)

    def test_json_output_handles_optional_fields(self, sample_system_profile, sample_model_profile):
        """Test that JSON output handles optional fields correctly."""
        # Create configuration with minimal required fields
        benchmark_result = BenchmarkResult(
            n_gpu_layers=0,
            prompt_speed_tps=5.0,
            generation_speed_tps=10.0,
            batch_size=None,  # Optional field
            parallel_level=None  # Optional field
        )
        
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=None,  # Optional field
            sampling_parameters=None,  # Optional field
            use_case=None,  # Optional field
            max_vram_gb=None  # Optional field
        )
        
        output = generate_output(config, "json")
        parsed_json = json.loads(output)
        
        # Should handle None values correctly
        assert parsed_json["best_benchmark_result"]["batch_size"] is None
        assert parsed_json["best_benchmark_result"]["parallel_level"] is None
        assert parsed_json["ctx_size"] is None
        assert parsed_json["sampling_parameters"] is None

    def test_json_output_handles_empty_lists(self, sample_model_profile, sample_benchmark_result):
        """Test that JSON output handles empty lists correctly."""
        # System profile with no GPUs
        system_profile = SystemProfile(
            cpu_cores=4,
            total_ram_gb=8.0,
            gpus=[],  # Empty list
            numa_detected=False,
            blas_backend="None"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],  # Empty list
            ctx_size=2048
        )
        
        output = generate_output(config, "json")
        parsed_json = json.loads(output)
        
        # Should handle empty lists correctly
        assert parsed_json["system_profile"]["gpus"] == []
        assert parsed_json["notes"] == []

    def test_json_output_schema_stability(self, sample_optimal_configuration):
        """Test that JSON output schema is stable and well-defined."""
        output = generate_output(sample_optimal_configuration, "json")
        parsed_json = json.loads(output)
        
        # AC 3: JSON schema is stable and well-defined
        # Check that all expected top-level keys are present
        expected_keys = {
            "system_profile", "model_profile", "best_benchmark_result", 
            "generated_command", "notes", "ctx_size", "sampling_parameters",
            "use_case", "max_vram_gb"
        }
        
        actual_keys = set(parsed_json.keys())
        assert expected_keys.issubset(actual_keys), f"Missing keys: {expected_keys - actual_keys}"

    def test_json_output_with_complex_sampling_parameters(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test JSON output with complex sampling parameters."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=["Test note"],
            ctx_size=4096,
            sampling_parameters={
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1,
                "seed": 42
            }
        )
        
        output = generate_output(config, "json")
        parsed_json = json.loads(output)
        
        # Should preserve sampling parameters correctly
        sampling_params = parsed_json["sampling_parameters"]
        assert sampling_params["temperature"] == 0.7
        assert sampling_params["top_p"] == 0.9
        assert sampling_params["top_k"] == 40
        assert sampling_params["repeat_penalty"] == 1.1
        assert sampling_params["seed"] == 42

    def test_json_output_with_multiple_gpus(self, sample_model_profile, sample_benchmark_result):
        """Test JSON output with multiple GPUs."""
        gpus = [
            GpuInfo(model_name="RTX 4090", vram_gb=24.0),
            GpuInfo(model_name="RTX 3080", vram_gb=10.0)
        ]
        
        system_profile = SystemProfile(
            cpu_cores=16,
            total_ram_gb=32.0,
            gpus=gpus,
            numa_detected=True,
            blas_backend="cuBLAS"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=["Multi-GPU configuration"],
            ctx_size=2048
        )
        
        output = generate_output(config, "json")
        parsed_json = json.loads(output)
        
        # Should handle multiple GPUs correctly
        gpus_data = parsed_json["system_profile"]["gpus"]
        assert len(gpus_data) == 2
        assert gpus_data[0]["model_name"] == "RTX 4090"
        assert gpus_data[0]["vram_gb"] == 24.0
        assert gpus_data[1]["model_name"] == "RTX 3080"
        assert gpus_data[1]["vram_gb"] == 10.0
