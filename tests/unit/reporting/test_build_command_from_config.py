"""
Unit tests for build_command_from_config function.
Tests the command assembly and syntax for various OptimalConfiguration inputs as required by Story 4.1 Task 4.
"""

import pytest
from llama_tune.reporting.output_generator import build_command_from_config
from llama_tune.core.data_models import OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult, GpuInfo


@pytest.fixture
def basic_system_profile():
    """Basic system profile with CPU cores and no GPUs."""
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[],
        numa_detected=False,
        blas_backend="None"
    )


@pytest.fixture
def basic_model_profile():
    """Basic model profile."""
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_0"
    )


@pytest.fixture
def basic_benchmark_result():
    """Basic benchmark result."""
    return BenchmarkResult(
        n_gpu_layers=0,
        prompt_speed_tps=10.0,
        generation_speed_tps=20.0,
        batch_size=None,
        parallel_level=None
    )


class TestBuildCommandFromConfig:
    """Test the build_command_from_config function for various configurations."""
    
    def test_basic_command_structure(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test that basic command structure is correct."""
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        command = build_command_from_config(config)
        
        # Should start with llama-server
        assert command.startswith("llama-server")
        
        # Should contain basic required parameters
        assert "--threads 8" in command
        assert "--model /path/to/model.gguf" in command
        assert "--ctx-size 2048" in command
        assert "--n-gpu-layers 0" in command
    
    def test_no_cpu_cores_detected(self, basic_model_profile, basic_benchmark_result):
        """Test command when no CPU cores are detected."""
        system_profile = SystemProfile(
            cpu_cores=0,
            total_ram_gb=16.0,
            gpus=[],
            numa_detected=False,
            blas_backend="None"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        command = build_command_from_config(config)
        
        # Should not include --threads when cpu_cores is 0
        assert "--threads" not in command
        assert "llama-server" in command
        assert "--model /path/to/model.gguf" in command
    
    def test_gpu_layers_from_benchmark(self, basic_system_profile, basic_model_profile):
        """Test that GPU layers come from benchmark result."""
        benchmark_result = BenchmarkResult(
            n_gpu_layers=15,
            prompt_speed_tps=10.0,
            generation_speed_tps=20.0,
            batch_size=None,
            parallel_level=None
        )
        
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        command = build_command_from_config(config)
        
        assert "--n-gpu-layers 15" in command
    
    def test_numa_detected(self, basic_model_profile, basic_benchmark_result):
        """Test NUMA flag when NUMA is detected."""
        system_profile = SystemProfile(
            cpu_cores=8,
            total_ram_gb=16.0,
            gpus=[],
            numa_detected=True,
            blas_backend="None"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        command = build_command_from_config(config)
        
        assert "--numa distribute" in command
    
    def test_cublas_backend(self, basic_model_profile, basic_benchmark_result):
        """Test cuBLAS flag when cuBLAS backend is detected."""
        system_profile = SystemProfile(
            cpu_cores=8,
            total_ram_gb=16.0,
            gpus=[GpuInfo(model_name="RTX 4090", vram_gb=24.0)],
            numa_detected=False,
            blas_backend="cuBLAS"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        command = build_command_from_config(config)
        
        assert "--use-cublas" in command
    
    def test_clblast_backend(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test CLBlast flag when CLBlast backend is detected."""
        system_profile = SystemProfile(
            cpu_cores=8,
            total_ram_gb=16.0,
            gpus=[],
            numa_detected=False,
            blas_backend="CLBlast"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        command = build_command_from_config(config)
        
        assert "--use-clblast" in command
    
    def test_sampling_parameters(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test that sampling parameters are included in command."""
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048,
            sampling_parameters={
                "temperature": 0.7,
                "top_p": 0.9,
                "repeat_penalty": 1.1,
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0
            }
        )
        
        command = build_command_from_config(config)
        
        assert "--temperature 0.7" in command
        assert "--top_p 0.9" in command
        assert "--repeat_penalty 1.1" in command
        assert "--presence_penalty 0.0" in command
        assert "--frequency_penalty 0.0" in command
    
    def test_sampling_parameters_with_none_values(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test that None values in sampling parameters are skipped."""
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048,
            sampling_parameters={
                "temperature": 0.7,
                "top_p": None,  # Should be skipped
                "repeat_penalty": 1.1,
                "presence_penalty": None  # Should be skipped
            }
        )
        
        command = build_command_from_config(config)
        
        assert "--temperature 0.7" in command
        assert "--top_p" not in command
        assert "--repeat_penalty 1.1" in command
        assert "--presence_penalty" not in command
    
    def test_no_context_size(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test command when context size is None."""
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=None
        )
        
        command = build_command_from_config(config)
        
        assert "--ctx-size" not in command
        assert "llama-server" in command
        assert "--model /path/to/model.gguf" in command
    
    def test_complex_configuration(self, basic_model_profile):
        """Test a complex configuration with multiple features enabled."""
        system_profile = SystemProfile(
            cpu_cores=16,
            total_ram_gb=64.0,
            gpus=[GpuInfo(model_name="RTX 4090", vram_gb=24.0)],
            numa_detected=True,
            blas_backend="cuBLAS"
        )
        
        benchmark_result = BenchmarkResult(
            n_gpu_layers=25,
            prompt_speed_tps=50.0,
            generation_speed_tps=100.0,
            batch_size=8,
            parallel_level=4
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=4096,
            sampling_parameters={
                "temperature": 0.8,
                "top_p": 0.95,
                "repeat_penalty": 1.05
            }
        )
        
        command = build_command_from_config(config)
        
        # Verify all expected components are present
        assert command.startswith("llama-server")
        assert "--threads 16" in command
        assert "--model /path/to/model.gguf" in command
        assert "--ctx-size 4096" in command
        assert "--temperature 0.8" in command
        assert "--top_p 0.95" in command
        assert "--repeat_penalty 1.05" in command
        assert "--n-gpu-layers 25" in command
        assert "--numa distribute" in command
        assert "--use-cublas" in command
    
    def test_command_syntax_is_valid(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test that the generated command has valid syntax (no double spaces, proper formatting)."""
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        command = build_command_from_config(config)
        
        # Should not have double spaces
        assert "  " not in command
        
        # Should not start or end with spaces
        assert not command.startswith(" ")
        assert not command.endswith(" ")
        
        # Should be a single line
        assert "\n" not in command
        assert "\r" not in command
        
        # Should have proper parameter format (--param value)
        parts = command.split()
        for i, part in enumerate(parts):
            if part.startswith("--") and i + 1 < len(parts):
                # Next part should be the value (not start with --)
                next_part = parts[i + 1]
                if not next_part.startswith("--"):
                    # This is expected for most parameters
                    continue

    def test_mlock_recommendation_integration(self, basic_model_profile, basic_benchmark_result):
        """Test that mlock recommendation is properly integrated into command."""
        # This test verifies integration with the should_recommend_mlock function
        system_profile = SystemProfile(
            cpu_cores=8,
            total_ram_gb=32.0,  # Large RAM to trigger mlock recommendation
            gpus=[],
            numa_detected=False,
            blas_backend="None"
        )

        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )

        # Mock the file size to be small enough to recommend mlock
        import unittest.mock
        with unittest.mock.patch('os.path.getsize', return_value=1 * 1024**3):  # 1GB model
            command = build_command_from_config(config)

        assert "--mlock" in command

    def test_empty_sampling_parameters(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test command with empty sampling parameters dict."""
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048,
            sampling_parameters={}
        )

        command = build_command_from_config(config)

        # Should not contain any sampling parameters
        sampling_params = ["temperature", "top_p", "repeat_penalty", "presence_penalty", "frequency_penalty"]
        for param in sampling_params:
            assert f"--{param}" not in command

    def test_model_path_with_spaces(self, basic_system_profile, basic_benchmark_result):
        """Test command with model path containing spaces."""
        model_profile = ModelProfile(
            file_path="/path/to/my model file.gguf",
            architecture="llama",
            layer_count=32,
            quantization_type="Q4_0"
        )

        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )

        command = build_command_from_config(config)

        assert "--model /path/to/my model file.gguf" in command

    def test_zero_gpu_layers(self, basic_system_profile, basic_model_profile):
        """Test command with zero GPU layers (CPU-only inference)."""
        benchmark_result = BenchmarkResult(
            n_gpu_layers=0,
            prompt_speed_tps=5.0,
            generation_speed_tps=10.0,
            batch_size=None,
            parallel_level=None
        )

        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )

        command = build_command_from_config(config)

        assert "--n-gpu-layers 0" in command

    def test_high_gpu_layers(self, basic_system_profile, basic_model_profile):
        """Test command with high number of GPU layers."""
        benchmark_result = BenchmarkResult(
            n_gpu_layers=50,
            prompt_speed_tps=100.0,
            generation_speed_tps=200.0,
            batch_size=None,
            parallel_level=None
        )

        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )

        command = build_command_from_config(config)

        assert "--n-gpu-layers 50" in command

    def test_unknown_blas_backend(self, basic_model_profile, basic_benchmark_result):
        """Test command with unknown BLAS backend (should not add any BLAS flags)."""
        system_profile = SystemProfile(
            cpu_cores=8,
            total_ram_gb=16.0,
            gpus=[],
            numa_detected=False,
            blas_backend="UnknownBLAS"
        )

        config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )

        command = build_command_from_config(config)

        # Should not contain any BLAS flags
        assert "--use-cublas" not in command
        assert "--use-clblast" not in command

    def test_command_order_consistency(self, basic_system_profile, basic_model_profile, basic_benchmark_result):
        """Test that command parameters appear in consistent order."""
        config = OptimalConfiguration(
            system_profile=basic_system_profile,
            model_profile=basic_model_profile,
            best_benchmark_result=basic_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048,
            sampling_parameters={"temperature": 0.7}
        )

        # Generate command multiple times to ensure consistency
        commands = [build_command_from_config(config) for _ in range(5)]

        # All commands should be identical
        for command in commands[1:]:
            assert command == commands[0]
