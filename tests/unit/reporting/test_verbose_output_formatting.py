"""
Unit tests for verbose output formatting as required by Story 4.2.
Tests the verbose summary formatting logic to ensure it correctly displays 
information from OptimalConfiguration objects according to acceptance criteria.
"""

import pytest
from llama_tune.reporting.output_generator import generate_output
from llama_tune.core.data_models import OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult, GpuInfo


@pytest.fixture
def sample_system_profile():
    """Sample system profile with multiple GPUs for testing."""
    return SystemProfile(
        cpu_cores=16,
        total_ram_gb=64.0,
        gpus=[
            GpuInfo(model_name="RTX 4090", vram_gb=24.0),
            GpuInfo(model_name="RTX 3080", vram_gb=10.0)
        ],
        numa_detected=True,
        blas_backend="cuBLAS"
    )


@pytest.fixture
def sample_model_profile():
    """Sample model profile for testing."""
    return ModelProfile(
        file_path="/path/to/llama-2-7b-chat.Q4_K_M.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )


@pytest.fixture
def sample_benchmark_result():
    """Sample benchmark result with statistical data."""
    return BenchmarkResult(
        n_gpu_layers=25,
        prompt_speed_tps=150.0,
        generation_speed_tps=75.0,
        batch_size=8,
        parallel_level=4,
        prompt_speed_tps_mean=150.0,
        prompt_speed_tps_std=5.0,
        generation_speed_tps_mean=75.0,
        generation_speed_tps_std=2.5,
        notes=["Benchmark completed successfully"]
    )


class TestVerboseOutputFormatting:
    """Test verbose output formatting according to Story 4.2 acceptance criteria."""
    
    def test_verbose_output_includes_system_hardware_info(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test AC 2: The summary must include detected hardware (CPU cores, RAM, GPU, VRAM)."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=4096
        )
        
        output = generate_output(config, "verbose")
        
        # Verify CPU cores are displayed
        assert "CPU Cores: 16" in output
        
        # Verify RAM is displayed
        assert "Total RAM (GB): 64.0" in output
        
        # Verify GPU count is displayed
        assert "GPUs: 2" in output
        
        # Verify individual GPU details with VRAM
        assert "RTX 4090 (24.0 GB VRAM)" in output
        assert "RTX 3080 (10.0 GB VRAM)" in output
        
        # Verify NUMA detection is displayed
        assert "NUMA Detected: True" in output
        
        # Verify BLAS backend is displayed
        assert "BLAS Backend: cuBLAS" in output
    
    def test_verbose_output_includes_model_information(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test that verbose output includes comprehensive model information."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=4096
        )
        
        output = generate_output(config, "verbose")
        
        # Verify model profile section exists
        assert "Model Profile:" in output
        
        # Verify model details are displayed
        assert "File Path: /path/to/llama-2-7b-chat.Q4_K_M.gguf" in output
        assert "Architecture: llama" in output
        assert "Layer Count: 32" in output
        assert "Quantization Type: Q4_K_M" in output
    
    def test_verbose_output_explains_gpu_layers_decision(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test AC 3: The summary must explain key decisions, such as the chosen --n-gpu-layers."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=4096
        )
        
        output = generate_output(config, "verbose")
        
        # Verify GPU layers decision is explained
        assert "GPU Layers: 25" in output
        assert "Best Benchmark Result:" in output
    
    def test_verbose_output_shows_performance_metrics(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test AC 3: The summary must explain the final benchmarked performance in tokens/second."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=4096
        )
        
        output = generate_output(config, "verbose")
        
        # Verify performance metrics are displayed with proper formatting
        assert "Prompt Speed (tps): 150.00 (StdDev: 5.00)" in output
        assert "Generation Speed (tps): 75.00 (StdDev: 2.50)" in output
        
        # Verify additional benchmark details
        assert "Batch Size: 8" in output
        assert "Parallel Level: 4" in output
    
    def test_verbose_output_includes_generated_command(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test that verbose output includes the generated command."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=4096
        )
        
        output = generate_output(config, "verbose")
        
        # Verify generated command section exists
        assert "Generated Command:" in output
        
        # Verify the command contains expected elements
        assert "llama-server" in output
        assert "--threads 16" in output
        assert "--model /path/to/llama-2-7b-chat.Q4_K_M.gguf" in output
        assert "--ctx-size 4096" in output
        assert "--n-gpu-layers 25" in output
        assert "--numa distribute" in output
        assert "--use-cublas" in output
    
    def test_verbose_output_includes_explanatory_notes(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test that verbose output includes explanatory notes about decisions."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=["Initial note"],
            ctx_size=4096
        )
        
        output = generate_output(config, "verbose")
        
        # Verify notes section exists
        assert "Notes:" in output
        
        # Verify explanatory notes are added for detected features
        assert "NUMA architecture detected" in output
        assert "cuBLAS backend detected" in output
        
        # Verify initial notes are preserved
        assert "Initial note" in output
    
    def test_verbose_output_handles_no_gpus(self, sample_model_profile, sample_benchmark_result):
        """Test verbose output when no GPUs are detected."""
        system_profile_no_gpu = SystemProfile(
            cpu_cores=8,
            total_ram_gb=16.0,
            gpus=[],
            numa_detected=False,
            blas_backend="OpenBLAS"
        )
        
        config = OptimalConfiguration(
            system_profile=system_profile_no_gpu,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )
        
        output = generate_output(config, "verbose")
        
        # Verify GPU count shows 0
        assert "GPUs: 0" in output
        
        # Verify BLAS backend is still shown
        assert "BLAS Backend: OpenBLAS" in output
        
        # Verify appropriate notes are added
        assert "OpenBLAS backend detected" in output
    
    def test_verbose_output_handles_zero_cpu_cores(self, sample_model_profile, sample_benchmark_result):
        """Test verbose output when CPU cores cannot be detected."""
        system_profile_no_cpu = SystemProfile(
            cpu_cores=0,
            total_ram_gb=16.0,
            gpus=[GpuInfo(model_name="RTX 4090", vram_gb=24.0)],
            numa_detected=False,
            blas_backend="cuBLAS"
        )

        config = OptimalConfiguration(
            system_profile=system_profile_no_cpu,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=2048
        )

        output = generate_output(config, "verbose")

        # Verify CPU cores shows 0
        assert "CPU Cores: 0" in output

        # Verify warning about CPU detection
        assert "Warning: Could not detect physical CPU cores" in output

        # Verify the generated command section doesn't include --threads
        # Extract just the command line from the output
        command_start = output.find("Generated Command:")
        command_section = output[command_start:] if command_start != -1 else ""
        command_line_start = command_section.find("llama-server")
        command_line_end = command_section.find("\n", command_line_start)
        command_line = command_section[command_line_start:command_line_end] if command_line_start != -1 else ""

        assert "--threads" not in command_line
    
    def test_verbose_output_structure_and_formatting(self, sample_system_profile, sample_model_profile, sample_benchmark_result):
        """Test that verbose output has proper structure and formatting."""
        config = OptimalConfiguration(
            system_profile=sample_system_profile,
            model_profile=sample_model_profile,
            best_benchmark_result=sample_benchmark_result,
            generated_command="",
            notes=[],
            ctx_size=4096
        )

        output = generate_output(config, "verbose")

        # Verify main sections are present in correct order
        sections = [
            "Optimal Configuration:",
            "System Profile:",
            "Model Profile:",
            "Best Benchmark Result:",
            "Generated Command:",
            "Notes:"
        ]

        for section in sections:
            assert section in output

        # Verify proper indentation for subsections
        lines = output.split('\n')
        subsection_items = [
            "CPU Cores:", "Total RAM", "File Path:", "GPU Layers:",
            "Architecture:", "Layer Count:", "Prompt Speed", "Generation Speed"
        ]

        # Check that we have properly indented subsection items
        found_subsection_items = []
        for line in lines:
            if line.startswith("  ") and not line.startswith("    "):
                for item in subsection_items:
                    if item in line:
                        found_subsection_items.append(item)
                        break

        # Verify we found at least some expected subsection items
        assert len(found_subsection_items) >= 5, f"Expected at least 5 subsection items, found: {found_subsection_items}"
