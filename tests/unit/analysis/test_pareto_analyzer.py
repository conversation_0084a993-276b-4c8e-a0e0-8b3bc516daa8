import pytest
from llama_tune.analysis.pareto_analyzer import is_dominated, get_pareto_front

def test_is_dominated():
    # Test case 1: p is dominated by q
    p1 = {"gen_speed": 10, "prompt_speed": 5, "layers": -2}
    q1 = {"gen_speed": 12, "prompt_speed": 6, "layers": -3} # Better in all objectives (including layers since -3 < -2 for minimization)
    objectives = ["gen_speed", "prompt_speed", "layers"]
    assert is_dominated(p1, q1, objectives) == True

    # Test case 2: p is not dominated by q (q is worse in one objective)
    p2 = {"gen_speed": 10, "prompt_speed": 5, "layers": -3}
    q2 = {"gen_speed": 8, "prompt_speed": 6, "layers": -2}
    assert is_dominated(p2, q2, objectives) == False

    # Test case 3: p is not dominated by q (q is equal in all objectives)
    p3 = {"gen_speed": 10, "prompt_speed": 5, "layers": -3}
    q3 = {"gen_speed": 10, "prompt_speed": 5, "layers": -3}
    assert is_dominated(p3, q3, objectives) == False

    # Test case 4: p is not dominated by q (q is better in one, worse in another)
    p4 = {"gen_speed": 10, "prompt_speed": 5, "layers": -3}
    q4 = {"gen_speed": 12, "prompt_speed": 4, "layers": -2}
    assert is_dominated(p4, q4, objectives) == False

def test_get_pareto_front():
    # Example from a common Pareto front explanation
    points = [
        {"id": "A", "gen_speed": 10, "prompt_speed": 10, "layers": -10}, # Pareto
        {"id": "B", "gen_speed": 8, "prompt_speed": 12, "layers": -8},  # Pareto
        {"id": "C", "gen_speed": 12, "prompt_speed": 8, "layers": -12},  # Pareto
        {"id": "D", "gen_speed": 7, "prompt_speed": 9, "layers": -7},   # Dominated by A, B, C
        {"id": "E", "gen_speed": 9, "prompt_speed": 9, "layers": -9},   # Dominated by A, B, C
    ]
    objectives = ["gen_speed", "prompt_speed", "layers"]
    pareto_front = get_pareto_front(points, objectives)
    
    # Sort by ID for consistent assertion
    expected_pareto = [
        {"id": "A", "gen_speed": 10, "prompt_speed": 10, "layers": -10},
        {"id": "B", "gen_speed": 8, "prompt_speed": 12, "layers": -8},
        {"id": "C", "gen_speed": 12, "prompt_speed": 8, "layers": -12},
    ]
    assert sorted(pareto_front, key=lambda x: x["id"]) == sorted(expected_pareto, key=lambda x: x["id"])

    # Test case with no dominated points (all are Pareto optimal)
    points2 = [
        {"id": "X", "gen_speed": 10, "prompt_speed": 1},
        {"id": "Y", "gen_speed": 1, "prompt_speed": 10},
    ]
    objectives2 = ["gen_speed", "prompt_speed"]
    pareto_front2 = get_pareto_front(points2, objectives2)
    assert sorted(pareto_front2, key=lambda x: x["id"]) == sorted(points2, key=lambda x: x["id"])

    # Test case with all points dominated (empty Pareto front)
    points3 = [
        {"id": "P", "gen_speed": 5, "prompt_speed": 5},
        {"id": "Q", "gen_speed": 10, "prompt_speed": 10},
    ]
    objectives3 = ["gen_speed", "prompt_speed"]
    pareto_front3 = get_pareto_front(points3, objectives3)
    assert sorted(pareto_front3, key=lambda x: x["id"]) == sorted([{"id": "Q", "gen_speed": 10, "prompt_speed": 10}], key=lambda x: x["id"])

    # Test case with empty input
    points4 = []
    objectives4 = ["gen_speed", "prompt_speed"]
    pareto_front4 = get_pareto_front(points4, objectives4)
    assert pareto_front4 == []
