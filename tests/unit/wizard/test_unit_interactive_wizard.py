import pytest
import typer
from unittest.mock import MagicMock, patch
from llama_tune.wizard.interactive_wizard import InteractiveWizard
from llama_tune.core.sampling_presets import SAMPLING_PRESETS

@pytest.fixture
def wizard():
    return InteractiveWizard()

@patch('questionary.select')
def test_select_use_case_conversational_chatbot(mock_select, wizard):
    mock_select.return_value.ask.return_value = "Conversational Chatbot"
    wizard._select_use_case()
    assert wizard.state["selected_use_case"] == "Conversational Chatbot"
    assert wizard.state["sampling_parameters"] == SAMPLING_PRESETS["Conversational Chatbot"]
    assert not wizard.state["prioritize_prompt_speed"]

@patch('questionary.select')
def test_select_use_case_rag_document_analysis(mock_select, wizard):
    mock_select.return_value.ask.return_value = "RAG / Document Analysis"
    wizard._select_use_case()
    assert wizard.state["selected_use_case"] == "RAG / Document Analysis"
    assert wizard.state["sampling_parameters"] == SAMPLING_PRESETS["RAG / Document Analysis"]
    assert wizard.state["prioritize_prompt_speed"]

@patch('questionary.select')
def test_select_use_case_code_completion(mock_select, wizard):
    mock_select.return_value.ask.return_value = "Code Completion"
    wizard._select_use_case()
    assert wizard.state["selected_use_case"] == "Code Completion"
    assert wizard.state["sampling_parameters"] == SAMPLING_PRESETS["Code Completion"]
    assert not wizard.state["prioritize_prompt_speed"]

@patch('questionary.select')
def test_select_use_case_custom_max_performance(mock_select, wizard):
    mock_select.return_value.ask.return_value = "Custom / Max Performance"
    wizard._select_use_case()
    assert wizard.state["selected_use_case"] == "Custom / Max Performance"
    assert wizard.state["sampling_parameters"] == {}
    assert not wizard.state["prioritize_prompt_speed"]

@patch('questionary.select')
def test_select_use_case_no_selection(mock_select, wizard):
    mock_select.return_value.ask.return_value = None
    with pytest.raises(typer.Exit):
        wizard._select_use_case()

@patch('questionary.text')
@patch('typer.echo')
def test_prompt_context_size_valid_input(mock_echo, mock_text, wizard):
    mock_text.return_value.ask.return_value = "2048"
    wizard._prompt_context_size()
    assert wizard.state["ctx_size"] == 2048
    mock_echo.assert_called_with("Context size set to: 2048")

@patch('questionary.text')
@patch('typer.echo')
def test_prompt_context_size_invalid_then_valid_input(mock_echo, mock_text, wizard):
    mock_text.return_value.ask.side_effect = ["abc", "1024"]
    wizard._prompt_context_size()
    assert wizard.state["ctx_size"] == 1024
    mock_echo.assert_any_call("Invalid input. Please enter a positive integer for context size.")
    mock_echo.assert_any_call("Context size set to: 1024")

@patch('questionary.text')
@patch('typer.echo')
def test_prompt_context_size_zero_input(mock_echo, mock_text, wizard):
    mock_text.return_value.ask.side_effect = ["0", "512"]
    wizard._prompt_context_size()
    assert wizard.state["ctx_size"] == 512
    mock_echo.assert_any_call("Context size must be a positive integer. Please try again.")
    mock_echo.assert_any_call("Context size set to: 512")

@patch('questionary.text')
@patch('typer.echo')
def test_prompt_context_size_negative_input(mock_echo, mock_text, wizard):
    mock_text.return_value.ask.side_effect = ["-100", "4096"]
    wizard._prompt_context_size()
    assert wizard.state["ctx_size"] == 4096
    mock_echo.assert_any_call("Context size must be a positive integer. Please try again.")
    mock_echo.assert_any_call("Context size set to: 4096")

@patch('questionary.text')
@patch('typer.echo')
def test_prompt_context_size_no_input_exits(mock_echo, mock_text, wizard):
    mock_text.return_value.ask.return_value = None
    with pytest.raises(typer.Exit):
        wizard._prompt_context_size()
    mock_echo.assert_called_with("No context size entered. Exiting wizard.")