import pytest
import subprocess
import platform
from unittest.mock import patch, MagicMock

from llama_tune.analyzer.analyzer import get_gpu_info, _detect_nvidia_gpu, _detect_apple_metal_gpu, _detect_amd_gpu
from llama_tune.core.data_models import GpuInfo

# Mock subprocess.run for all tests
@pytest.fixture(autouse=True)
def mock_subprocess_run():
    with patch('subprocess.run') as mock_run:
        yield mock_run

# Test cases for _detect_nvidia_gpu
def test_detect_nvidia_gpu_success(mock_subprocess_run):
    mock_subprocess_run.return_value = MagicMock(
        stdout="NVIDIA GeForce RTX 4070, 12282 MiB\nNVIDIA GeForce RTX 3060, 12288 MiB",
        stderr="",
        returncode=0
    )
    gpus = _detect_nvidia_gpu()
    assert len(gpus) == 2
    assert gpus[0] == GpuInfo(model_name="NVIDIA GeForce RTX 4070", vram_gb=11.99)
    assert gpus[1] == GpuInfo(model_name="NVIDIA GeForce RTX 3060", vram_gb=12.0)
    mock_subprocess_run.assert_called_once_with(
        ["nvidia-smi", "--query-gpu=name,memory.total", "--format=csv,noheader,nounits"],
        capture_output=True, text=True, check=True, timeout=10
    )

def test_detect_nvidia_gpu_no_gpu(mock_subprocess_run):
    mock_subprocess_run.return_value = MagicMock(
        stdout="",
        stderr="No devices found",
        returncode=0
    )
    gpus = _detect_nvidia_gpu()
    assert len(gpus) == 0

def test_detect_nvidia_gpu_nvidia_smi_not_found(mock_subprocess_run):
    mock_subprocess_run.side_effect = FileNotFoundError
    gpus = _detect_nvidia_gpu()
    assert len(gpus) == 0

def test_detect_nvidia_gpu_command_error(mock_subprocess_run):
    mock_subprocess_run.side_effect = subprocess.CalledProcessError(1, "nvidia-smi")
    gpus = _detect_nvidia_gpu()
    assert len(gpus) == 0

def test_detect_nvidia_gpu_timeout(mock_subprocess_run):
    mock_subprocess_run.side_effect = subprocess.TimeoutExpired("nvidia-smi", 10)
    gpus = _detect_nvidia_gpu()
    assert len(gpus) == 0

# Test cases for _detect_apple_metal_gpu
@patch('platform.system', return_value='Darwin')
def test_detect_apple_metal_gpu_success(mock_platform_system, mock_subprocess_run):
    mock_subprocess_run.return_value = MagicMock(
        stdout="""
Graphics/Displays:

    Apple M1:
      VRAM (Total): 8 GB
      Chipset Model: Apple M1

    Apple M2 Pro:
      VRAM (Total): 16 GB
      Chipset Model: Apple M2 Pro
""",
        stderr="",
        returncode=0
    )
    gpus = _detect_apple_metal_gpu()
    assert len(gpus) == 2
    assert gpus[0] == GpuInfo(model_name="Apple M1", vram_gb=8.0)
    assert gpus[1] == GpuInfo(model_name="Apple M2 Pro", vram_gb=16.0)
    mock_subprocess_run.assert_called_once_with(
        ["system_profiler", "SPDisplaysDataType"],
        capture_output=True, text=True, check=True, timeout=10
    )

@patch('platform.system', return_value='Darwin')
def test_detect_apple_metal_gpu_no_gpu(mock_platform_system, mock_subprocess_run):
    mock_subprocess_run.return_value = MagicMock(
        stdout="""
Graphics/Displays:
    No GPUs found.
""",
        stderr="",
        returncode=0
    )
    gpus = _detect_apple_metal_gpu()
    assert len(gpus) == 0

@patch('platform.system', return_value='Darwin')
def test_detect_apple_metal_gpu_system_profiler_not_found(mock_platform_system, mock_subprocess_run):
    mock_subprocess_run.side_effect = FileNotFoundError
    gpus = _detect_apple_metal_gpu()
    assert len(gpus) == 0

@patch('platform.system', return_value='Darwin')
def test_detect_apple_metal_gpu_command_error(mock_platform_system, mock_subprocess_run):
    mock_subprocess_run.side_effect = subprocess.CalledProcessError(1, "system_profiler")
    gpus = _detect_apple_metal_gpu()
    assert len(gpus) == 0

@patch('platform.system', return_value='Darwin')
def test_detect_apple_metal_gpu_timeout(mock_platform_system, mock_subprocess_run):
    mock_subprocess_run.side_effect = subprocess.TimeoutExpired("system_profiler", 10)
    gpus = _detect_apple_metal_gpu()
    assert len(gpus) == 0

@patch('platform.system', return_value='Linux') # Not macOS
def test_detect_apple_metal_gpu_non_darwin(mock_platform_system, mock_subprocess_run):
    gpus = _detect_apple_metal_gpu()
    assert len(gpus) == 0
    mock_subprocess_run.assert_not_called()

# Test cases for _detect_amd_gpu
def test_detect_amd_gpu_success(mock_subprocess_run):
    mock_subprocess_run.return_value = MagicMock(
        stdout="card,product_name,vram_total_mb\n0,AMD Radeon RX 6800 XT,16384\n1,AMD Radeon RX 5700,8192",
        stderr="",
        returncode=0
    )
    gpus = _detect_amd_gpu()
    assert len(gpus) == 2
    assert gpus[0] == GpuInfo(model_name="AMD Radeon RX 6800 XT", vram_gb=16.0)
    assert gpus[1] == GpuInfo(model_name="AMD Radeon RX 5700", vram_gb=8.0)
    mock_subprocess_run.assert_called_once_with(
        ["rocm-smi", "--showproductname", "--showmeminfo", "vram", "--csv"],
        capture_output=True, text=True, check=True, timeout=10
    )

def test_detect_amd_gpu_no_gpu(mock_subprocess_run):
    mock_subprocess_run.return_value = MagicMock(
        stdout="card,product_name,vram_total_mb\n",
        stderr="",
        returncode=0
    )
    gpus = _detect_amd_gpu()
    assert len(gpus) == 0

def test_detect_amd_gpu_rocm_smi_not_found(mock_subprocess_run):
    mock_subprocess_run.side_effect = FileNotFoundError
    gpus = _detect_amd_gpu()
    assert len(gpus) == 0

def test_detect_amd_gpu_command_error(mock_subprocess_run):
    mock_subprocess_run.side_effect = subprocess.CalledProcessError(1, "rocm-smi")
    gpus = _detect_amd_gpu()
    assert len(gpus) == 0

def test_detect_amd_gpu_timeout(mock_subprocess_run):
    mock_subprocess_run.side_effect = subprocess.TimeoutExpired("rocm-smi", 10)
    gpus = _detect_amd_gpu()
    assert len(gpus) == 0

# Test cases for get_gpu_info (integration of all detectors)
@patch('platform.system', return_value='Linux') # Ensure not Darwin
@patch('llama_tune.analyzer.analyzer._detect_nvidia_gpu', return_value=[GpuInfo(model_name="NVIDIA GPU", vram_gb=10.0)])
@patch('llama_tune.analyzer.analyzer._detect_apple_metal_gpu', return_value=[])
@patch('llama_tune.analyzer.analyzer._detect_amd_gpu', return_value=[])
def test_get_gpu_info_only_nvidia(mock_amd, mock_apple, mock_nvidia, mock_platform_system):
    gpus = get_gpu_info()
    assert len(gpus) == 1
    assert gpus[0] == GpuInfo(model_name="NVIDIA GPU", vram_gb=10.0)
    mock_nvidia.assert_called_once()
    mock_apple.assert_not_called() # Should not be called on Linux
    mock_amd.assert_called_once()

@patch('platform.system', return_value='Darwin')
@patch('llama_tune.analyzer.analyzer._detect_nvidia_gpu', return_value=[])
@patch('llama_tune.analyzer.analyzer._detect_apple_metal_gpu', return_value=[GpuInfo(model_name="Apple GPU", vram_gb=16.0)])
@patch('llama_tune.analyzer.analyzer._detect_amd_gpu', return_value=[])
def test_get_gpu_info_only_apple(mock_amd, mock_apple, mock_nvidia, mock_platform_system):
    gpus = get_gpu_info()
    assert len(gpus) == 1
    assert gpus[0] == GpuInfo(model_name="Apple GPU", vram_gb=16.0)
    mock_nvidia.assert_called_once()
    mock_apple.assert_called_once()
    mock_amd.assert_called_once()

@patch('platform.system', return_value='Linux') # Ensure not Darwin
@patch('llama_tune.analyzer.analyzer._detect_nvidia_gpu', return_value=[])
@patch('llama_tune.analyzer.analyzer._detect_apple_metal_gpu', return_value=[])
@patch('llama_tune.analyzer.analyzer._detect_amd_gpu', return_value=[GpuInfo(model_name="AMD GPU", vram_gb=12.0)])
def test_get_gpu_info_only_amd(mock_amd, mock_apple, mock_nvidia, mock_platform_system):
    gpus = get_gpu_info()
    assert len(gpus) == 1
    assert gpus[0] == GpuInfo(model_name="AMD GPU", vram_gb=12.0)
    mock_nvidia.assert_called_once()
    mock_apple.assert_not_called() # Should not be called on Linux
    mock_amd.assert_called_once()

@patch('platform.system', return_value='Linux') # Ensure not Darwin
@patch('llama_tune.analyzer.analyzer._detect_nvidia_gpu', return_value=[])
@patch('llama_tune.analyzer.analyzer._detect_apple_metal_gpu', return_value=[])
@patch('llama_tune.analyzer.analyzer._detect_amd_gpu', return_value=[])
def test_get_gpu_info_no_gpus_found(mock_amd, mock_apple, mock_nvidia, mock_platform_system):
    gpus = get_gpu_info()
    assert len(gpus) == 0
    mock_nvidia.assert_called_once()
    mock_apple.assert_not_called() # Should not be called on Linux
    mock_amd.assert_called_once()

@patch('platform.system', return_value='Darwin')
@patch('llama_tune.analyzer.analyzer._detect_nvidia_gpu', return_value=[GpuInfo(model_name="NVIDIA GPU", vram_gb=10.0)])
@patch('llama_tune.analyzer.analyzer._detect_apple_metal_gpu', return_value=[GpuInfo(model_name="Apple GPU", vram_gb=16.0)])
@patch('llama_tune.analyzer.analyzer._detect_amd_gpu', return_value=[GpuInfo(model_name="AMD GPU", vram_gb=12.0)])
def test_get_gpu_info_multiple_gpus(mock_amd, mock_apple, mock_nvidia, mock_platform_system):
    gpus = get_gpu_info()
    assert len(gpus) == 3
    assert GpuInfo(model_name="NVIDIA GPU", vram_gb=10.0) in gpus
    assert GpuInfo(model_name="Apple GPU", vram_gb=16.0) in gpus
    assert GpuInfo(model_name="AMD GPU", vram_gb=12.0) in gpus