import pytest
import platform
import subprocess
import logging
from unittest.mock import patch, MagicMock

from llama_tune.analyzer.cpu_analyzer import get_physical_cpu_cores, _get_physical_cpu_cores_linux, _get_physical_cpu_cores_windows, _get_physical_cpu_cores_macos

# Configure logging for tests
logging.basicConfig(level=logging.WARNING)

@pytest.fixture(autouse=True)
def mock_logger():
    """Fixture to mock the logger in cpu_analyzer.py."""
    with patch('llama_tune.analyzer.cpu_analyzer.logger') as mock_log:
        yield mock_log

@patch('platform.system', return_value='Linux')
@patch('subprocess.run')
def test_get_physical_cpu_cores_linux_success(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Linux detection success."""
    mock_subprocess_run.return_value = MagicMock(
        stdout="""
Architecture:        x86_64
CPU op-mode(s):      32-bit, 64-bit
Byte Order:          Little Endian
CPU(s):              8
On-line CPU(s) list: 0-7
Core(s) per socket:  4
Socket(s):           2
NUMA node(s):        1
Vendor ID:           GenuineIntel
CPU family:          6
Model:               158
Model name:          Intel(R) Core(TM) i7-7700HQ CPU @ 2.80GHz
Stepping:            9
CPU MHz:             2808.000
BogoMIPS:            5616.00
Virtualization:      VT-x
L1d cache:           32K
L1i cache:           32K
L2 cache:            256K
L3 cache:            6MB
NUMA node0 CPU(s):   0-7
Flags:               fpu vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush dts acpi mmx fxsr sse sse2 ss ht tm pbe syscall nx pdpe1gb rdtscp lm constant_tsc art arch_perfmon pebs bts rep_good nopl xtopology nonstop_tsc cpuid aperfmperf tsc_known_freq pni pclmulqdq dtes64 monitor ds_cpl vmx smx est tm2 ssse3 sdbg fma cx16 xtpr pdcm pcid sse4_1 sse4_2 x2apic movbe popcnt tsc_deadline_timer aes xsave avx f16c rdrand lahf_lm abm 3dnowprefetch epb intel_pt tpr_shadow vnmi flexpriority ept vpid fsgsbase tsc_adjust bmi1 hle avx2 smep bmi2 erms rtm rdseed adx smap clflushopt xsaveopt xsavec xgetbv1 xsaves dtherm ida arat pln pts
"""
    )
    assert get_physical_cpu_cores() == 8
    mock_logger.warning.assert_not_called()

@patch('platform.system', return_value='Linux')
@patch('subprocess.run', side_effect=FileNotFoundError)
def test_get_physical_cpu_cores_linux_lscpu_not_found(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Linux detection when lscpu is not found."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("'lscpu' command not found. Cannot detect physical CPU cores on Linux.")

@patch('platform.system', return_value='Linux')
@patch('subprocess.run', return_value=MagicMock(stdout="Invalid output", stderr=""))
def test_get_physical_cpu_cores_linux_parse_fail(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Linux detection when lscpu output cannot be parsed."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("Could not parse 'lscpu' output for physical core count on Linux.")

@patch('platform.system', return_value='Windows')
@patch('subprocess.run')
def test_get_physical_cpu_cores_windows_success(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Windows detection success."""
    mock_subprocess_run.return_value = MagicMock(
        stdout="""

NumberOfCores=4



NumberOfCores=4


"""
    )
    assert get_physical_cpu_cores() == 8
    mock_logger.warning.assert_not_called()

@patch('platform.system', return_value='Windows')
@patch('subprocess.run', side_effect=FileNotFoundError)
def test_get_physical_cpu_cores_windows_wmic_not_found(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Windows detection when wmic is not found."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("'wmic' command not found. Cannot detect physical CPU cores on Windows.")

@patch('platform.system', return_value='Windows')
@patch('subprocess.run', return_value=MagicMock(stdout="Invalid output", stderr=""))
def test_get_physical_cpu_cores_windows_parse_fail(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Windows detection when wmic output cannot be parsed."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("Could not determine physical core count from WMIC on Windows.")

@patch('platform.system', return_value='Darwin')
@patch('subprocess.run')
def test_get_physical_cpu_cores_macos_success(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test macOS detection success."""
    mock_subprocess_run.return_value = MagicMock(stdout="8\n")
    assert get_physical_cpu_cores() == 8
    mock_logger.warning.assert_not_called()

@patch('platform.system', return_value='Darwin')
@patch('subprocess.run', side_effect=FileNotFoundError)
def test_get_physical_cpu_cores_macos_sysctl_not_found(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test macOS detection when sysctl is not found."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("'sysctl' command not found. Cannot detect physical CPU cores on macOS.")

@patch('platform.system', return_value='Darwin')
@patch('subprocess.run', return_value=MagicMock(stdout="Invalid output", stderr=""))
def test_get_physical_cpu_cores_macos_parse_fail(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test macOS detection when sysctl output cannot be parsed."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("Could not parse 'sysctl hw.physicalcpu' output for physical core count on macOS.")

@patch('platform.system', return_value='UnsupportedOS')
def test_get_physical_cpu_cores_unsupported_os(mock_platform_system, mock_logger):
    """Test unsupported OS."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("Unsupported operating system: UnsupportedOS. Cannot detect physical CPU cores.")

@patch('platform.system', return_value='Linux')
@patch('subprocess.run', side_effect=subprocess.CalledProcessError(1, 'lscpu', stderr='Error'))
def test_get_physical_cpu_cores_linux_subprocess_error(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Linux detection with subprocess error."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("Error running 'lscpu' on Linux: Command 'lscpu' returned non-zero exit status 1.. Stderr: Error")

@patch('platform.system', return_value='Windows')
@patch('subprocess.run', side_effect=subprocess.CalledProcessError(1, 'wmic', stderr='Error'))
def test_get_physical_cpu_cores_windows_subprocess_error(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test Windows detection with subprocess error."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("Error running 'wmic' on Windows: Command 'wmic' returned non-zero exit status 1.. Stderr: Error")

@patch('platform.system', return_value='Darwin')
@patch('subprocess.run', side_effect=subprocess.CalledProcessError(1, 'sysctl', stderr='Error'))
def test_get_physical_cpu_cores_macos_subprocess_error(mock_subprocess_run, mock_platform_system, mock_logger):
    """Test macOS detection with subprocess error."""
    assert get_physical_cpu_cores() == 0
    mock_logger.warning.assert_called_with("Error running 'sysctl' on macOS: Command 'sysctl' returned non-zero exit status 1.. Stderr: Error")