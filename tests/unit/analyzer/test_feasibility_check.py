import pytest
from unittest.mock import patch, MagicMock
import os
import logging

from llama_tune.analyzer.analyzer import estimate_memory_footprint, run_feasibility_check, FeasibilityError
from llama_tune.core.data_models import ModelProfile, SystemProfile, GpuInfo

# Configure logging for tests
logging.basicConfig(level=logging.INFO)

@pytest.fixture(autouse=True)
def mock_logger():
    """Fixture to mock the logger in analyzer.py."""
    with patch('llama_tune.analyzer.analyzer.logger') as mock_log:
        yield mock_log

@pytest.fixture
def mock_os_path_getsize(mocker):
    """Fixture to mock os.path.getsize."""
    return mocker.patch("os.path.getsize")

@pytest.fixture
def mock_system_profile_with_gpus():
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=32.0,
        gpus=[GpuInfo(model_name="NVIDIA RTX 3080", vram_gb=10.0)],
        numa_detected=False,
        blas_backend="cublas"
    )

@pytest.fixture
def mock_system_profile_no_gpus():
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[],
        numa_detected=False,
        blas_backend="openblas"
    )

# --- Tests for estimate_memory_footprint ---

@pytest.mark.parametrize("quantization_type, expected_ram_multiplier, expected_vram_multiplier", [
    ("Q4_0", 1.5, 0.5),
    ("Q4_K_M", 1.5, 0.5),
    ("Q5_K_M", 1.5, 0.625),
    ("Q8_0", 1.5, 1.0),
    ("F16", 1.5, 2.0),
    ("F32", 1.5, 4.0),
    ("UNKNOWN", 1.5, 1.0), # Default to Q8_0 equivalent
])
def test_estimate_memory_footprint_quantization_types(mock_os_path_getsize, quantization_type, expected_ram_multiplier, expected_vram_multiplier):
    """Test memory estimation for various quantization types."""
    mock_os_path_getsize.return_value = 10 * (1024**3) # 10 GB model file size
    model_profile = ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type=quantization_type
    )
    
    estimated_ram, estimated_vram = estimate_memory_footprint(model_profile)
    
    assert estimated_ram == pytest.approx(10.0 * expected_ram_multiplier)
    assert estimated_vram == pytest.approx(10.0 * expected_vram_multiplier)

def test_estimate_memory_footprint_file_not_found(mock_os_path_getsize, mock_logger):
    """Test memory estimation when model file is not found."""
    mock_os_path_getsize.side_effect = FileNotFoundError
    model_profile = ModelProfile(
        file_path="/path/to/non_existent_model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )
    
    estimated_ram, estimated_vram = estimate_memory_footprint(model_profile)
    
    assert estimated_ram == 0.0
    assert estimated_vram == 0.0
    mock_logger.warning.assert_called_with(
        "Model file not found for size estimation: /path/to/non_existent_model.gguf. Assuming 0 GB."
    )

def test_estimate_memory_footprint_getsize_error(mock_os_path_getsize, mock_logger):
    """Test memory estimation when os.path.getsize raises a generic error."""
    mock_os_path_getsize.side_effect = Exception("Permission denied")
    model_profile = ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )
    
    estimated_ram, estimated_vram = estimate_memory_footprint(model_profile)
    
    assert estimated_ram == 0.0
    assert estimated_vram == 0.0
    mock_logger.warning.assert_called_with(
        "Could not get model file size for /path/to/model.gguf: Permission denied. Assuming 0 GB."
    )

# --- Tests for run_feasibility_check ---

def test_run_feasibility_check_pass_with_gpus(mock_os_path_getsize, mock_system_profile_with_gpus, mock_logger):
    """Test feasibility check passing with sufficient RAM and VRAM."""
    mock_os_path_getsize.return_value = 5 * (1024**3) # 5 GB model (Q4_K_M -> 2.5 GB VRAM, 7.5 GB RAM)
    model_profile = ModelProfile(
        file_path="/path/to/small_model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )
    
    # System: 32 GB RAM, 10 GB VRAM
    # Estimated: ~7.5 GB RAM, ~2.5 GB VRAM
    # Available: 32 - 4 = 28 GB RAM, 10 - 0.5 = 9.5 GB VRAM
    
    run_feasibility_check(model_profile, mock_system_profile_with_gpus)
    mock_logger.info.assert_called_with("Feasibility check passed. Model is likely to fit within system resources.")

def test_run_feasibility_check_pass_no_gpus(mock_os_path_getsize, mock_system_profile_no_gpus, mock_logger):
    """Test feasibility check passing with sufficient RAM and no GPUs."""
    mock_os_path_getsize.return_value = 5 * (1024**3) # 5 GB model (Q4_K_M -> 2.5 GB VRAM, 7.5 GB RAM)
    model_profile = ModelProfile(
        file_path="/path/to/small_model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )
    
    # System: 16 GB RAM, 0 GB VRAM
    # Estimated: ~7.5 GB RAM, ~2.5 GB VRAM (VRAM estimate is ignored if no GPUs)
    # Available: 16 - 4 = 12 GB RAM
    
    run_feasibility_check(model_profile, mock_system_profile_no_gpus)
    mock_logger.info.assert_called_with("Feasibility check passed. Model is likely to fit within system resources.")
    mock_logger.warning.assert_called_with("Model has VRAM requirements but no GPUs were detected. Assuming CPU-only inference.")


def test_run_feasibility_check_fail_ram(mock_os_path_getsize, mock_system_profile_no_gpus):
    """Test feasibility check failing due to insufficient RAM."""
    mock_os_path_getsize.return_value = 10 * (1024**3) # 10 GB model (Q4_K_M -> 5 GB VRAM, 15 GB RAM)
    model_profile = ModelProfile(
        file_path="/path/to/large_model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )
    
    # System: 16 GB RAM, 0 GB VRAM
    # Estimated: ~15 GB RAM
    # Available: 16 - 4 = 12 GB RAM
    
    with pytest.raises(FeasibilityError, match="Insufficient system RAM detected"):
        run_feasibility_check(model_profile, mock_system_profile_no_gpus)

def test_run_feasibility_check_fail_vram(mock_os_path_getsize, mock_system_profile_with_gpus):
    """Test feasibility check failing due to insufficient VRAM."""
    mock_os_path_getsize.return_value = 8 * (1024**3) # 8 GB model (F16 -> 16 GB VRAM, 24 GB RAM)
    model_profile = ModelProfile(
        file_path="/path/to/vram_hungry_model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="F16"
    )
    
    # System: 32 GB RAM, 10 GB VRAM
    # Estimated: ~24 GB RAM, ~16 GB VRAM
    # Available: 32 - 4 = 28 GB RAM (RAM check passes), 10 - 0.5 = 9.5 GB VRAM (VRAM check fails)
    
    with pytest.raises(FeasibilityError, match="Insufficient GPU VRAM detected"):
        run_feasibility_check(model_profile, mock_system_profile_with_gpus)

def test_run_feasibility_check_zero_ram_detection(mock_os_path_getsize, mock_system_profile_with_gpus, mock_logger):
    """Test behavior when system RAM detection returns 0."""
    mock_os_path_getsize.return_value = 1 * (1024**3) # 1 GB model
    model_profile = ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )
    
    system_profile_zero_ram = SystemProfile(
        cpu_cores=8,
        total_ram_gb=0.0, # Simulate 0 RAM detection
        gpus=[GpuInfo(model_name="NVIDIA RTX 3080", vram_gb=10.0)],
        numa_detected=False,
        blas_backend="cublas"
    )
    
    with pytest.raises(FeasibilityError, match="Insufficient system RAM detected"):
        run_feasibility_check(model_profile, system_profile_zero_ram)
    
    # Ensure the warning from get_system_profile is still logged
    # The exact log message might vary, so we check if 'Performing pre-flight feasibility check...'
    # is among the calls, and that a warning about VRAM is also present if no GPUs.
    # We need to check all calls, not just the last one.
    assert any("Performing pre-flight feasibility check..." in call.args[0] for call in mock_logger.info.call_args_list)
    # In this specific test case (zero RAM), the primary failure is RAM, so VRAM warning is not expected.
    # The VRAM warning is for scenarios where VRAM is estimated but no GPUs are present, and RAM is sufficient.