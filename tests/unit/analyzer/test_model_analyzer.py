import pytest
from unittest import mock
import os
import subprocess

from llama_tune.analyzer.analyzer import get_model_profile, ModelParsingError
from llama_tune.core.data_models import ModelProfile

# Define paths to fixture files
FIXTURE_DIR = os.path.join(os.path.dirname(__file__), "fixtures")
SUCCESS_OUTPUT_PATH = os.path.join(FIXTURE_DIR, "llama_gguf_output_success.txt")
INVALID_FILE_OUTPUT_PATH = os.path.join(FIXTURE_DIR, "llama_gguf_output_failure_invalid_file.txt")
MISSING_FIELD_OUTPUT_PATH = os.path.join(FIXTURE_DIR, "llama_gguf_output_failure_missing_field.txt")

@pytest.fixture
def mock_subprocess_run(mocker):
    """Fixture to mock subprocess.run."""
    return mocker.patch("subprocess.run")

@pytest.fixture
def mock_os_path_exists(mocker):
    """Fixture to mock os.path.exists."""
    return mocker.patch("os.path.exists")

@pytest.fixture
def mock_os_path_isfile(mocker):
    """Fixture to mock os.path.isfile."""
    return mocker.patch("os.path.isfile")

def read_fixture(filepath: str) -> str:
    """Helper to read content from a fixture file."""
    with open(filepath, "r") as f:
        return f.read()

def test_get_model_profile_success(mock_subprocess_run, mock_os_path_exists, mock_os_path_isfile):
    """Test successful parsing of a valid GGUF model."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = True
    mock_subprocess_run.return_value = mock.Mock(
        stdout=read_fixture(SUCCESS_OUTPUT_PATH),
        stderr="",
        returncode=0
    )

    model_path = "/path/to/model.gguf"
    profile = get_model_profile(model_path)

    mock_subprocess_run.assert_called_once_with(
        ["llama-gguf", model_path], capture_output=True, text=True, check=True, timeout=60
    )
    assert profile == ModelProfile(
        file_path=model_path,
        architecture="llama",
        layer_count=32,
        quantization_type="Q4_K_M"
    )

def test_get_model_profile_file_not_found(mock_os_path_exists, mock_os_path_isfile):
    """Test case where the model file does not exist."""
    mock_os_path_exists.return_value = False
    mock_os_path_isfile.return_value = False # Ensure it's not considered a file

    model_path = "/path/to/non_existent_model.gguf"
    with pytest.raises(ModelParsingError, match=f"Model file not found at: {model_path}"):
        get_model_profile(model_path)

def test_get_model_profile_path_not_file(mock_os_path_exists, mock_os_path_isfile):
    """Test case where the path exists but is not a file (e.g., a directory)."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = False

    model_path = "/path/to/a_directory/"
    with pytest.raises(ModelParsingError, match=f"Path is not a file: {model_path}"):
        get_model_profile(model_path)

def test_get_model_profile_invalid_gguf_file(mock_subprocess_run, mock_os_path_exists, mock_os_path_isfile):
    """Test case where llama-gguf reports an invalid GGUF file."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = True
    mock_subprocess_run.side_effect = subprocess.CalledProcessError(
        returncode=1,
        cmd=["llama-gguf", "/path/to/invalid.txt"],
        stderr=read_fixture(INVALID_FILE_OUTPUT_PATH)
    )

    model_path = "/path/to/invalid.txt"
    with pytest.raises(ModelParsingError, match="is not a valid GGUF model"):
        get_model_profile(model_path)

def test_get_model_profile_missing_metadata_field(mock_subprocess_run, mock_os_path_exists, mock_os_path_isfile):
    """Test case where llama-gguf output is missing a required metadata field."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = True
    mock_subprocess_run.return_value = mock.Mock(
        stdout=read_fixture(MISSING_FIELD_OUTPUT_PATH),
        stderr="",
        returncode=0
    )

    model_path = "/path/to/model_missing_field.gguf"
    with pytest.raises(ModelParsingError, match="Failed to extract all required metadata"):
        get_model_profile(model_path)

def test_get_model_profile_llama_gguf_not_found(mock_subprocess_run, mock_os_path_exists, mock_os_path_isfile):
    """Test case where the llama-gguf executable is not found."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = True
    mock_subprocess_run.side_effect = FileNotFoundError("llama-gguf")

    model_path = "/path/to/model.gguf"
    with pytest.raises(ModelParsingError, match="The 'llama-gguf' executable was not found"):
        get_model_profile(model_path)

def test_get_model_profile_command_timeout(mock_subprocess_run, mock_os_path_exists, mock_os_path_isfile):
    """Test case where the llama-gguf command times out."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = True
    mock_subprocess_run.side_effect = subprocess.TimeoutExpired(
        cmd=["llama-gguf", "/path/to/model.gguf"], timeout=60
    )

    model_path = "/path/to/model.gguf"
    with pytest.raises(ModelParsingError, match="llama-gguf command timed out"):
        get_model_profile(model_path)

def test_get_model_profile_generic_subprocess_error(mock_subprocess_run, mock_os_path_exists, mock_os_path_isfile):
    """Test case for a generic CalledProcessError from llama-gguf."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = True
    mock_subprocess_run.side_effect = subprocess.CalledProcessError(
        returncode=1,
        cmd=["llama-gguf", "/path/to/model.gguf"],
        stderr="Some other unexpected error from llama-gguf"
    )

    model_path = "/path/to/model.gguf"
    with pytest.raises(ModelParsingError, match="llama-gguf command failed"):
        get_model_profile(model_path)

def test_get_model_profile_unexpected_error(mock_subprocess_run, mock_os_path_exists, mock_os_path_isfile):
    """Test case for any other unexpected exception."""
    mock_os_path_exists.return_value = True
    mock_os_path_isfile.return_value = True
    mock_subprocess_run.side_effect = Exception("Something completely unexpected happened")

    model_path = "/path/to/model.gguf"
    with pytest.raises(ModelParsingError, match="An unexpected error occurred"):
        get_model_profile(model_path)