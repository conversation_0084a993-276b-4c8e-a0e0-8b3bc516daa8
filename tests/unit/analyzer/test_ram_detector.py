import pytest
import psutil
from unittest.mock import patch, MagicMock
import logging

from llama_tune.analyzer.analyzer import get_total_ram_gb

# Configure logging for tests
logging.basicConfig(level=logging.WARNING)

@pytest.fixture(autouse=True)
def mock_logger():
    """Fixture to mock the logger in analyzer.py."""
    with patch('llama_tune.analyzer.analyzer.logger') as mock_log:
        yield mock_log

@patch('psutil.virtual_memory')
def test_get_total_ram_gb_success(mock_virtual_memory, mock_logger):
    """Test successful RAM detection."""
    mock_virtual_memory.return_value = MagicMock(total=16 * (1024**3)) # 16 GB
    assert get_total_ram_gb() == 16.0
    mock_logger.warning.assert_not_called()

@patch('psutil.virtual_memory')
def test_get_total_ram_gb_zero_ram(mock_virtual_memory, mock_logger):
    """Test RAM detection with 0 bytes."""
    mock_virtual_memory.return_value = MagicMock(total=0)
    assert get_total_ram_gb() == 0.0
    mock_logger.warning.assert_not_called()

@patch('psutil.virtual_memory', side_effect=Exception("Test error"))
def test_get_total_ram_gb_failure(mock_virtual_memory, mock_logger):
    """Test RAM detection failure."""
    assert get_total_ram_gb() == 0.0
    mock_logger.warning.assert_called_with("Could not detect total RAM: Test error")

@patch('psutil.virtual_memory')
def test_get_total_ram_gb_rounding(mock_virtual_memory, mock_logger):
    """Test RAM detection with rounding."""
    mock_virtual_memory.return_value = MagicMock(total=16.12345 * (1024**3)) # 16.12345 GB
    assert get_total_ram_gb() == 16.12
    mock_logger.warning.assert_not_called()