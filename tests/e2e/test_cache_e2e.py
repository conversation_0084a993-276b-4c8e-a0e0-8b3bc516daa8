"""
End-to-end tests for cache functionality.

Tests the cache flags work correctly:
1. --force flag is recognized
2. --no-cache flag is recognized
3. CLI accepts these flags without errors
"""

import pytest
from typer.testing import C<PERSON><PERSON>unner

from llama_tune.cli import app


@pytest.fixture
def runner():
    """CLI test runner."""
    return CliRunner()


class TestCacheE2E:
    """End-to-end tests for cache functionality."""

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    @patch('llama_tune.caching.cache_manager.CacheManager.__init__')
    def test_cache_workflow_first_run_then_cached_run(self, mock_cache_init, mock_get_model_profile,
                                                     mock_get_system_profile, runner, temp_cache_dir,
                                                     mock_system_profile, mock_model_profile,
                                                     mock_benchmark_result):
        """Test complete cache workflow: first run caches, second run uses cache."""
        # Setup mocks
        mock_cache_init.return_value = None
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        
        # Mock the cache manager to use our temp directory
        with patch('llama_tune.caching.cache_manager.CacheManager') as MockCacheManager:
            from llama_tune.caching.cache_manager import CacheManager
            mock_cache_instance = CacheManager(cache_dir=temp_cache_dir)
            MockCacheManager.return_value = mock_cache_instance
            
            # Mock benchmark execution
            with patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._run_gpu_offload_benchmark') as mock_gpu_benchmark:
                mock_gpu_results = [mock_benchmark_result]
                mock_gpu_benchmark.return_value = (mock_benchmark_result, mock_gpu_results)
                
                # First run - should execute benchmark and cache result
                result1 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1"
                ])
                
                assert result1.exit_code == 0
                # Verify benchmark was executed
                mock_gpu_benchmark.assert_called()
                
                # Reset mock call count
                mock_gpu_benchmark.reset_mock()
                
                # Second run - should use cache and not execute benchmark
                result2 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1"
                ])
                
                assert result2.exit_code == 0
                # Verify benchmark was NOT executed (cache hit)
                mock_gpu_benchmark.assert_not_called()

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    @patch('llama_tune.caching.cache_manager.CacheManager.__init__')
    def test_force_flag_bypasses_cache(self, mock_cache_init, mock_get_model_profile,
                                      mock_get_system_profile, runner, temp_cache_dir,
                                      mock_system_profile, mock_model_profile,
                                      mock_benchmark_result):
        """Test that --force flag bypasses cache and runs full benchmark."""
        # Setup mocks
        mock_cache_init.return_value = None
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        
        # Mock the cache manager to use our temp directory
        with patch('llama_tune.caching.cache_manager.CacheManager') as MockCacheManager:
            from llama_tune.caching.cache_manager import CacheManager
            mock_cache_instance = CacheManager(cache_dir=temp_cache_dir)
            MockCacheManager.return_value = mock_cache_instance
            
            # Mock benchmark execution
            with patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._run_gpu_offload_benchmark') as mock_gpu_benchmark:
                mock_gpu_results = [mock_benchmark_result]
                mock_gpu_benchmark.return_value = (mock_benchmark_result, mock_gpu_results)
                
                # First run - populate cache
                result1 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1"
                ])

                assert result1.exit_code == 0
                mock_gpu_benchmark.assert_called()

                # Reset mock call count
                mock_gpu_benchmark.reset_mock()

                # Second run with --force - should bypass cache and run benchmark
                result2 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1",
                    "--force"
                ])
                
                assert result2.exit_code == 0
                # Verify benchmark was executed despite cache entry existing
                mock_gpu_benchmark.assert_called()

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    def test_no_cache_flag_disables_caching(self, mock_get_model_profile, mock_get_system_profile,
                                           runner, mock_system_profile, mock_model_profile,
                                           mock_benchmark_result):
        """Test that --no-cache flag disables caching entirely."""
        # Setup mocks
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        
        # Mock benchmark execution
        with patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._run_gpu_offload_benchmark') as mock_gpu_benchmark:
            mock_gpu_results = [mock_benchmark_result]
            mock_gpu_benchmark.return_value = (mock_benchmark_result, mock_gpu_results)
            
            # Run with --no-cache flag
            result = runner.invoke(app, [
                "--benchmark",
                "--model-path", "/path/to/test_model.gguf",
                "--ctx-size", "2048",
                "--num-runs", "1",
                "--no-cache"
            ])
            
            assert result.exit_code == 0
            # Verify benchmark was executed
            mock_gpu_benchmark.assert_called()

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    @patch('llama_tune.caching.cache_manager.CacheManager.__init__')
    def test_different_parameters_create_different_cache_entries(self, mock_cache_init,
                                                               mock_get_model_profile,
                                                               mock_get_system_profile,
                                                               runner, temp_cache_dir,
                                                               mock_system_profile,
                                                               mock_model_profile,
                                                               mock_benchmark_result):
        """Test that different benchmark parameters create separate cache entries."""
        # Setup mocks
        mock_cache_init.return_value = None
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        
        # Mock the cache manager to use our temp directory
        with patch('llama_tune.caching.cache_manager.CacheManager') as MockCacheManager:
            from llama_tune.caching.cache_manager import CacheManager
            mock_cache_instance = CacheManager(cache_dir=temp_cache_dir)
            MockCacheManager.return_value = mock_cache_instance
            
            # Mock benchmark execution
            with patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._run_gpu_offload_benchmark') as mock_gpu_benchmark:
                mock_gpu_results = [mock_benchmark_result]
                mock_gpu_benchmark.return_value = (mock_benchmark_result, mock_gpu_results)
                
                # First run with ctx_size=2048
                result1 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1"
                ])

                assert result1.exit_code == 0
                assert mock_gpu_benchmark.call_count == 1

                # Second run with ctx_size=4096 (different parameter)
                result2 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "4096",
                    "--num-runs", "1"
                ])

                assert result2.exit_code == 0
                # Should execute benchmark again due to different ctx_size
                assert mock_gpu_benchmark.call_count == 2

                # Reset mock call count
                mock_gpu_benchmark.reset_mock()

                # Third run with ctx_size=2048 again (should use cache)
                result3 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1"
                ])
                
                assert result3.exit_code == 0
                # Should NOT execute benchmark (cache hit)
                mock_gpu_benchmark.assert_not_called()

    @patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile')
    @patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile')
    @patch('llama_tune.caching.cache_manager.CacheManager.__init__')
    def test_json_output_with_cache(self, mock_cache_init, mock_get_model_profile,
                                   mock_get_system_profile, runner, temp_cache_dir,
                                   mock_system_profile, mock_model_profile,
                                   mock_benchmark_result):
        """Test that JSON output works correctly with caching."""
        # Setup mocks
        mock_cache_init.return_value = None
        mock_get_system_profile.return_value = mock_system_profile
        mock_get_model_profile.return_value = mock_model_profile
        
        # Mock the cache manager to use our temp directory
        with patch('llama_tune.caching.cache_manager.CacheManager') as MockCacheManager:
            from llama_tune.caching.cache_manager import CacheManager
            mock_cache_instance = CacheManager(cache_dir=temp_cache_dir)
            MockCacheManager.return_value = mock_cache_instance
            
            # Mock benchmark execution
            with patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._run_gpu_offload_benchmark') as mock_gpu_benchmark:
                mock_gpu_results = [mock_benchmark_result]
                mock_gpu_benchmark.return_value = (mock_benchmark_result, mock_gpu_results)
                
                # First run with JSON output
                result1 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1",
                    "--json"
                ])

                assert result1.exit_code == 0
                # Verify output is valid JSON
                try:
                    json.loads(result1.stdout)
                except json.JSONDecodeError:
                    pytest.fail("Output is not valid JSON")

                # Reset mock call count
                mock_gpu_benchmark.reset_mock()

                # Second run with JSON output (should use cache)
                result2 = runner.invoke(app, [
                    "--benchmark",
                    "--model-path", "/path/to/test_model.gguf",
                    "--ctx-size", "2048",
                    "--num-runs", "1",
                    "--json"
                ])
                
                assert result2.exit_code == 0
                # Verify benchmark was NOT executed (cache hit)
                mock_gpu_benchmark.assert_not_called()
                # Verify output is still valid JSON
                try:
                    json.loads(result2.stdout)
                except json.JSONDecodeError:
                    pytest.fail("Cached output is not valid JSON")
