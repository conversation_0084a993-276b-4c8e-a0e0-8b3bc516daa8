import json
import time
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/completion', methods=['POST'])
def completion():
    data = request.json
    n_gpu_layers = data.get('n_gpu_layers', 0)
    batch_size = data.get('batch_size', 512)
    parallel_level = data.get('parallel', 1)
    prompt = data.get('prompt', '')

    # Simulate different performance based on parameters
    # This is a simplified simulation. In a real scenario, you might have a more complex mapping.
    if n_gpu_layers > 0 and batch_size > 100 and parallel_level > 1:
        generation_speed_tps = 15.0  # Optimal
    elif n_gpu_layers == 0:
        generation_speed_tps = 5.0   # CPU only, slower
    else:
        generation_speed_tps = 10.0  # Sub-optimal GPU usage

    # Simulate OOM for specific conditions (example)
    if batch_size > 2048:
        return jsonify({"error": "Out of memory"}), 500

    # Simulate some processing time
    time.sleep(0.1)

    response = {
        "content": f"Mock completion for prompt: {prompt}",
        "generation_speed_tps": generation_speed_tps,
        "n_gpu_layers": n_gpu_layers,
        "batch_size": batch_size,
        "parallel_level": parallel_level
    }
    return jsonify(response)

if __name__ == '__main__':
    # This will be run by the subprocess, so we need to ensure it's accessible
    # For testing, we'll use a specific port.
    app.run(host='127.0.0.1', port=8080)