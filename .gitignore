# Byte-compiled / optimized / C files
__pycache__/
*.pyc
*.pyo
*.pyd
*.so

# Distribution / packaging
build/
dist/
*.egg-info/
*.egg

# Virtual Environments
# Poetry typically creates the virtual env outside the project,
# but this covers cases where it's created locally.
.venv
venv/
env/

# Pytest
.pytest_cache/
.coverage
.coverage.*
htmlcov/

# IDE / Editor specific
.vscode/
.idea/

# OS-specific
.DS_Store
Thumbs.db

# BMAD Framework / IDE specific
.bmad-core/
.claude/
.roomodes
web-bundles/
.windsurf/
.clinerules/
rich_output_debug.txt