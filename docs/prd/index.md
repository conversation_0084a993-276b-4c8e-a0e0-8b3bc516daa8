# llama-tune: Optimal Settings Generator for llama-server

## Table of Contents

- [llama-tune: Optimal Settings Generator for llama-server](#table-of-contents)
  - [Change Log](#change-log)
  - [1. Introduction & Problem Statement](#1-introduction-problem-statement)
  - [2. Goals and Objectives](#2-goals-and-objectives)
  - [3. User Person<PERSON>](#3-user-personas)
  - [4. Technical Assumptions](#4-technical-assumptions)
  - [5. Functional Requirements (Epics & User Stories)](#5-functional-requirements-epics-user-stories)
  - [6. Non-Functional Requirements](#6-non-functional-requirements)
  - [7. Out of Scope (for V1)](#7-out-of-scope-for-v1)
