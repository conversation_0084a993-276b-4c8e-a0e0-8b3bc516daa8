# **6. Non-Functional Requirements**
* **Platform Compatibility:** Buildable and runnable on Linux, macOS (Metal), and Windows.
* **Performance:** Analysis/generation should be fast (< 1 minute). Benchmark should aim for 5-15 minutes.
* **Usability:** Intuitive CLI with a clear `--help` message.
* **Reliability:** Handle errors gracefully and be robust to child process crashes, interpreting OOM failures as a hardware limit.
