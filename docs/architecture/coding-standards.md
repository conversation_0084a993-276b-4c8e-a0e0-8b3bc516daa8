# **Coding Standards**

  * **Formatting & Linting**: Code will be formatted with **Black** and linted with **Ruff**.
  * **Type Hinting**: All function signatures must include type hints.
  * **Naming**: Standard PEP 8 conventions will be followed (`snake_case` for functions/variables, `PascalCase` for classes).
  * **Critical Rules**: All external process calls must be centralized in their respective components; Data Models must be used for data transfer between components; specific exceptions must be caught; all user-facing output must be handled by the `Output Generator`.
