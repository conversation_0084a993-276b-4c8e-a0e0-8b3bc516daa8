# **Components**

## **1. CLI Interface**

  * **Responsibility:** The main entry point; parses CLI arguments and delegates control.
  * **Key Interfaces:** `main(args)`
  * **Dependencies:** `Interactive Wizard`, `Benchmarking Engine`
  * **Technology Stack:** Python, Typer

## **2. Hardware & Model Analyzer (Revised)**

  * **Responsibility:** To detect system hardware and parse GGUF model metadata by executing the `llama-gguf` utility and parsing its output.
  * **Key Interfaces:** `get_system_profile() -> SystemProfile`, `get_model_profile(path: str) -> ModelProfile`
  * **Dependencies:** External `llama-gguf` executable.
  * **Technology Stack:** Python

## **3. Interactive Wizard**

  * **Responsibility:** To orchestrate the guided question-and-answer session.
  * **Key Interfaces:** `run_wizard() -> OptimalConfiguration`
  * **Dependencies:** `Hardware & Model Analyzer`, `Output Generator`
  * **Technology Stack:** Python

## **4. Benchmarking Engine**

  * **Responsibility:** To run the multi-phase benchmark, calling `llama-bench` and `llama-server` as external processes and analyzing their performance.
  * **Key Interfaces:** `run_benchmark(model: ModelProfile, system: SystemProfile) -> BenchmarkResult`
  * **Dependencies:** `Hardware & Model Analyzer`, `Cache Manager`, external `llama-bench`/`llama-server` executables.
  * **Technology Stack:** Python (`subprocess` module).

## **5. Cache Manager**

  * **Responsibility:** To save and retrieve `OptimalConfiguration` objects to/from a local file-based cache.
  * **Key Interfaces:** `get_from_cache(key: str) -> Optional[OptimalConfiguration]`, `save_to_cache(key: str, config: OptimalConfiguration)`
  * **Dependencies:** None.
  * **Technology Stack:** Python (`pathlib`, `json`).

## **6. Output Generator**

  * **Responsibility:** To format the final `OptimalConfiguration` object for display based on the output mode (`--verbose`, `--json`, or default).
  * **Key Interfaces:** `generate_output(config: OptimalConfiguration, mode: str)`
  * **Dependencies:** None.
  * **Technology Stack:** Python.
