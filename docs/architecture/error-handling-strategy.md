# **Error Handling Strategy**

  * **General**: The tool will use custom exceptions inheriting from a base `LlamaTuneError` for clear, specific error handling. Unhandled errors will be caught at the top level, and a user-friendly message will be printed to `stderr`.
  * **Logging**: Python's built-in `logging` module will be used. A `--debug` flag will enable verbose logging. Logs go to `stderr` to separate them from the primary output on `stdout`.
  * **External Processes**: Failures in external processes (`llama-bench`, etc.) will be caught gracefully. The process's `stderr` will be logged for debugging.
