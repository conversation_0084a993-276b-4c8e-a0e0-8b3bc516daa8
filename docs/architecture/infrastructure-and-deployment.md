# **Infrastructure and Deployment**

This project will be distributed as a Python package on PyPI.

  * **Packaging**: **Poetry** will be used to build the package into a distributable format.
  * **CI/CD Pipeline**: A **GitHub Actions** pipeline will be configured to automatically test, build, and publish the package to PyPI whenever a new version tag is pushed to the `main` branch.
  * **End-User Installation**: The user will install the tool via `pip install llama-tune`.
  * **Prerequisite**: This workflow requires a PyPI account and an API token to be configured as a secret in the GitHub repository.
