# **Source Tree**

```plaintext
llama-tune/
├── .github/
│   └── workflows/              # CI/CD pipelines (e.g., for testing & publishing)
│       └── release.yml
├── docs/                       # Project documentation
│   ├── prd.md
│   └── architecture.md
├── src/
│   └── llama_tune/             # Main application package
│       ├── __init__.py
│       ├── analyzer/             # Hardware & Model Analyzer component
│       ├── benchmarker/          # Benchmarking Engine component
│       ├── caching/              # Cache Manager component
│       ├── cli.py                # Main CLI Interface (Typer app)
│       ├── core/                 # Shared core logic, including Data Models
│       ├── reporting/            # Output Generator component
│       └── wizard/               # Interactive Wizard component
├── tests/                      # All tests
│   ├── e2e/                    # End-to-end tests for the CLI
│   ├── integration/            # Integration tests for component interactions
│   └── unit/                   # Unit tests for individual modules
├── .gitignore                  # Git ignore file
├── pyproject.toml              # Poetry configuration and dependencies
└── README.md                   # Project README
```
