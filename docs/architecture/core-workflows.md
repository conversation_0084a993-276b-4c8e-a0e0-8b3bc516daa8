# **Core Workflows**

## **Workflow 1: Automated Benchmark (`--benchmark`)**

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant Benchmarking<PERSON><PERSON><PERSON> as BE
    participant <PERSON><PERSON><PERSON><PERSON><PERSON> as CM
    participant Analyzer
    participant OutputGenerator as OG
    actor Ext_Processes as "llama-bench/server"

    User->>CLI: runs `llama-tune --benchmark ...`
    CLI->>BE: run_benchmark()
    BE->>CM: get_from_cache(key)
    
    alt Cache Miss
        CM-->>BE: null
        BE->>Analyzer: get_system_profile(), get_model_profile()
        Analyzer-->>BE: SystemProfile, ModelProfile
        
        loop Benchmark Phases
            BE->>Ext_Processes: execute(params)
            Ext_Processes-->>BE: performance data
        end

        BE->>CM: save_to_cache(key, results)
        BE->>OG: generate_output(results)
        OG-->>CLI: formatted string
        CLI-->>User: prints command & summary
    else Cache Hit
        CM-->>BE: OptimalConfiguration
        BE->>OG: generate_output(cached_config)
        OG-->>CLI: formatted string
        CLI-->>User: prints command & summary
    end
```

## **Workflow 2: Interactive Wizard (`--interactive`)**

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant InteractiveWizard as WIZ
    participant Analyzer
    participant OutputGenerator as OG

    User->>CLI: runs `llama-tune --interactive ...`
    CLI->>WIZ: run_wizard()
    WIZ->>User: "Choose Use Case..."
    User-->>WIZ: Selection (e.g., "Chatbot")
    WIZ->>User: "Enter Context Size..."
    User-->>WIZ: Value (e.g., 4096)

    WIZ->>Analyzer: get_system_profile(), get_model_profile()
    Analyzer-->>WIZ: SystemProfile, ModelProfile

    WIZ->>OG: generate_output(config_from_choices)
    OG-->>CLI: formatted string
    CLI-->>User: prints command & summary
```
