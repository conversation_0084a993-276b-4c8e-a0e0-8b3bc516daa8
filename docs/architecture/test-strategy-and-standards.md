# **Test Strategy and Standards**

  * **Philosophy**: A "test-after" approach will be used with a target of 85% code coverage, enforced by the CI pipeline.
  * **Test Types**:
      * **Unit Tests (`tests/unit/`)**: To test functions in isolation, using `pytest` and `pytest-mock`.
      * **Integration Tests (`tests/integration/`)**: To test interactions between internal components.
      * **E2E Tests (`tests/e2e/`)**: To test the full CLI command, using mocked external processes.
  * **Continuous Testing**: The GitHub Actions pipeline will run the full `pytest` suite on every pull request and push, blocking merges on failure.
