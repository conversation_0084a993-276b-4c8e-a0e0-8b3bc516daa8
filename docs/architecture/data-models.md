# **Data Models**

## **SystemProfile**

  * **Purpose:** To hold all detected hardware specifications of the user's machine.
  * **Key Attributes:** `cpu_cores: int`, `total_ram_gb: float`, `gpus: List[GpuInfo]`, `numa_detected: bool`, `blas_backend: str`
  * **Relationships:** Contains a list of `GpuInfo` models.

## **GpuInfo**

  * **Purpose:** To store the specifications of a single detected GPU.
  * **Key Attributes:** `model_name: str`, `vram_gb: float`
  * **Relationships:** Is contained within the `SystemProfile` model.

## **ModelProfile**

  * **Purpose:** To hold all key metadata extracted from the user's GGUF model file.
  * **Key Attributes:** `file_path: str`, `architecture: str`, `layer_count: int`, `quantization_type: str`
  * **Relationships:** Primary input data model.

## **BenchmarkResult**

  * **Purpose:** To store the performance metrics from a single benchmark run.
  * **Key Attributes:** `n_gpu_layers: int`, `prompt_speed_tps: float`, `generation_speed_tps: float`, `batch_size: Optional[int]`, `parallel_level: Optional[int]`
  * **Relationships:** A list of these is analyzed to find the `OptimalConfiguration`.

## **OptimalConfiguration**

  * **Purpose:** The final, comprehensive data object representing the tool's complete output. This structure is saved to the cache.
  * **Key Attributes:** `system_profile: SystemProfile`, `model_profile: ModelProfile`, `best_benchmark_result: BenchmarkResult`, `generated_command: str`, `notes: List[str]`
  * **Relationships:** The top-level data model that aggregates all other information.
