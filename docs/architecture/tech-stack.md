# **Tech Stack**

## **Technology Stack Table**

| Category         | Technology | Version | Purpose                        | Rationale                                                                        |
| :--------------- | :--------- | :------ | :----------------------------- | :------------------------------------------------------------------------------- |
| **Language** | Python     | 3.11+   | Core application language      | User preference; excellent ecosystem for scripting and system interaction.      |
| **Runtime** | Python     | 3.11+   | The environment the code runs in | Using a recent, stable version provides modern features and long-term support.   |
| **Framework** | Typer      | latest  | CLI framework                  | Agreed upon previously; modern, intuitive, and powerful due to its `Click` foundation. |
| **Testing** | pytest     | latest  | Testing framework              | The de-facto standard for Python testing; its fixture system is excellent for all tiers of our testing strategy. |
| **Dependency Mgmt** | Poetry     | latest  | Dependency & package management | A modern tool that provides dependency locking for reproducible builds and simplifies virtual environment management. |
| **Build Tool** | Poetry     | latest  | Application Packaging          | Poetry has robust, built-in capabilities for building and publishing packages to PyPI. |
