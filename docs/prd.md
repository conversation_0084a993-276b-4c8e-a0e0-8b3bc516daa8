# **llama-tune: Optimal Settings Generator for llama-server**

Author: <PERSON>, BMAD Fullstack Team
Status: Final v1.2 (Fully Detailed)
Date: 2025-06-23

## **Change Log**

| Date       | Version | Description                                           | Author  |
| :--- | :------ | :---------------------------------------------------- | :------ |
| 2025-06-23 | 1.0     | Initial version finalized with detailed user stories. | <PERSON> (PM) |
| 2025-06-23 | 1.1     | Corrected heading levels for sharding compatibility.  | <PERSON> (PO) |

## **1. Introduction & Problem Statement**
The llama.cpp project, particularly its server executable, is an exceptionally powerful tool, but its extensive configurability presents a significant barrier to entry and a complex optimization challenge. Users struggle to answer the fundamental question: **"What are the best settings for my specific hardware and this specific model?"** This leads to suboptimal performance, resource mismanagement (OOM errors), and a time-consuming trial-and-error process. This document proposes a tool to automate finding the best-performing and most stable configuration for a user's environment.

## **2. Goals and Objectives**
* **Primary Goal:** To drastically simplify the process of configuring llama-server for optimal performance and stability.
* **Key Objectives:**
    1.  Automate System and Model Analysis.
    2.  Provide a complete, copy-pasteable `llama-server` command as output.
    3.  Maximize performance (tokens/second) within hardware constraints.
    4.  Ensure stability and avoid OOM errors.
    5.  Educate the user with human-readable explanations.

## **3. User Personas**
1.  **The Novice ("Plug-and-Play Paul"):** Wants to get a server running quickly without reading extensive documentation.
2.  **The Hobbyist/Gamer ("Tuning Tina"):** Wants to squeeze maximum performance from their consumer-grade hardware.
3.  **The Advanced User ("Researcher Randy"):** Wants to quickly establish reliable performance baselines for new models or systems.

## **4. Technical Assumptions**
* **Repository Structure**: Monorepo.
* **Service Architecture**: Modular Architecture (a single executable with internally distinct, testable modules).
* **Language and Framework**: Python with the Typer framework for the CLI.
* **Testing Requirements**: A three-tiered strategy of Unit, Integration, and End-to-End (E2E) tests.

## **5. Functional Requirements (Epics & User Stories)**

### **Epic 1: System & Model Hardware Analysis**

#### **Story 1.1: Detect Physical CPU Cores for Optimal Thread Count**
* As an Advanced User (Researcher Randy), I want the tool to automatically detect the number of physical CPU cores on my system, so that an optimal `--threads` value is recommended in the final command for maximum performance.
* **ACs:** 1. The tool correctly identifies physical (not logical/hyper-threaded) cores. 2. The generated command includes `--threads` with the correct value. 3. Detection works on Linux, Windows, and macOS. 4. If detection fails, the argument is omitted and a warning is logged.

#### **Story 1.2: Detect System RAM for Memory-Aware Configuration**
* As a Researcher (Randy), I want the tool to detect the total amount of system RAM so that it can make informed decisions about model loading and memory locking (`--mlock`).
* **ACs:** 1. Accurately detects total physical RAM in GB. 2. Detected RAM value is used for feasibility and `--mlock` checks. 3. Works on Linux, Windows, and macOS. 4. If detection fails, a warning is displayed and memory-dependent checks are skipped.

#### **Story 1.3: Detect GPU and VRAM for Optimal Offloading**
* As a Hobbyist (Tuning Tina), I want the tool to automatically detect my GPU(s), including model name and VRAM capacity, so that it can determine the maximum number of model layers to offload for the best performance.
* **ACs:** 1. Correctly identifies compatible GPUs. 2. Reports model name and VRAM for each GPU. 3. VRAM is used as the primary constraint for `--n-gpu-layers`. 4. Defaults to a CPU-only configuration if no compatible GPU is found.

#### **Story 1.4: Read Model Metadata**
* As a Novice (Plug-and-Play Paul), I want the tool to automatically read the metadata from my GGUF model file so that it understands the model's properties without me having to specify them manually.
* **ACs:** 1. Successfully parses GGUF file metadata. 2. Extracts model architecture, layer count, and quantization type. 3. Layer count is used for `--n-gpu-layers` calculation. 4. Exits with a clear error if the file is not a valid GGUF model.

#### **Story 1.5: Perform Pre-flight Feasibility Check**
* As a Novice (Plug-and-Play Paul), I want the tool to perform a quick feasibility check before running a full benchmark so that I'm immediately warned if the selected model is too large for my system's RAM or VRAM.
* **ACs:** 1. Estimates model memory footprint. 2. Compares footprint against detected RAM/VRAM. 3. Exits with a warning if the model is unlikely to fit. 4. Accounts for reasonable OS overhead.

#### **Story 1.6: Detect and Configure for NUMA Architecture**
* As an Advanced User (Researcher Randy) with a multi-socket server, I want the tool to detect if my system has a NUMA architecture so that it can recommend the optimal `--numa` settings for my hardware.
* **ACs:** 1. Determines if the system is NUMA-based. 2. Includes `--numa` flag in the command if NUMA is detected. 3. Explains the recommendation in verbose output. 4. Omits the argument if not NUMA or not supported.

#### **Story 1.7: Recommend Memory Lock for Stable Performance**
* As a Hobbyist (Tuning Tina), I want the tool to recommend using `--mlock` when appropriate so that the model is locked into RAM, preventing it from being swapped to disk and ensuring consistent performance.
* **ACs:** 1. Compares model size against available RAM. 2. Adds `--mlock` to the command if the model fits comfortably. 3. Omits `--mlock` if the model is too large. 4. Explains the rationale in verbose output.

#### **Story 1.8: Detect and Enable High-Performance BLAS Backend**
* As a Hobbyist (Tuning Tina), I want the tool to detect if my `llama.cpp` was compiled with a high-performance BLAS backend so that it can recommend enabling it for a potential speed boost.
* **ACs:** 1. Determines which BLAS backend is used. 2. Adds the appropriate flag (e.g., `--use-cublas`) if a high-performance backend is detected. 3. Adds no flag otherwise. 4. Explains the recommendation in verbose output.

### **Epic 2: Interactive Configuration Wizard**

#### **Story 2.1: Initiate Interactive Wizard**
* As a Novice (Plug-and-Play Paul), I want to start the tool with an `--interactive` flag to launch a guided setup wizard so that I can get a working configuration without needing to know all the technical parameters.
* **ACs:** 1. `--interactive` flag starts a step-by-step session. 2. Wizard prints a welcome message. 3. Wizard does not run without the flag. 4. Prompts are clear and easy to understand.

#### **Story 2.2: Select Configuration Preset by Use Case**
* As a Novice (Plug-and-Play Paul), I want the interactive wizard to ask me for my intended use case so that it can apply optimized performance and text-generation (sampling) presets automatically.
* **ACs:** 1. Presents a numbered list of use cases. 2. Applies the correct set of sampling parameters based on selection. 3. 'RAG' preset prioritizes prompt processing speed. 4. 'Custom' option skips sampling presets. 5. Confirms selection before proceeding.

#### **Story 2.3: Specify Target Context Size in Wizard**
* As a Hobbyist (Tuning Tina), I want the interactive wizard to ask for my desired context size so that I can control the trade-off between memory usage and the model's conversational history length.
* **ACs:** 1. Prompts user for `--ctx-size`. 2. Suggests a sensible default. 3. Validates input is a positive integer. 4. Uses the final value in the generated command.

#### **Story 2.4: Generate and Display Final Command**
* As a Novice (Plug-and-Play Paul), I want the wizard to generate and display the complete `llama-server` command after I've answered all questions so that I can easily copy and run it to start the server.
* **ACs:** 1. Compiles all detected info and user choices. 2. Constructs a valid `llama-server` command. 3. Prints the final command clearly to the console. 4. Concludes with a confirmation message.

### **Epic 3: Automated Performance Benchmarking**

#### **Story 3.1: Initiate Automated Benchmark**
* As a Hobbyist (Tuning Tina), I want to run the tool with a `--benchmark` flag and specify a target context size so that I can automatically find the most performant settings for my specific hardware and model.
* **ACs:** 1. `--benchmark` flag initiates the process. 2. Requires model file and `--ctx-size` as arguments. 3. Exits with an error if arguments are missing. 4. Executes full analysis from Epic 1 as a prerequisite.

#### **Story 3.2: Benchmark GPU Layer Offload and Throughput**
* As a Hobbyist (Tuning Tina), I want the benchmark tool to systematically test different numbers of GPU layers (`--n-gpu-layers`) so that it can find the maximum number my VRAM can handle while measuring both prompt processing and token generation speed.
* **ACs:** 1. Iteratively increases `--n-gpu-layers`. 2. Runs a standardized test load for each value. 3. Separately measures prompt processing and token generation speed. 4. Gracefully handles OOM errors as the VRAM limit. 5. Identifies the optimal `--n-gpu-layers` value.

#### **Story 3.3: Benchmark Continuous Batching for Server Throughput**
* As a Researcher (Randy), I want the benchmark tool to test and tune continuous batching parameters so that I can find the optimal configuration for maximum server throughput under concurrent load.
* **ACs:** 1. Runs after optimal `--n-gpu-layers` is found. 2. Iteratively tests batching parameters. 3. Simulates concurrent requests. 4. Identifies configuration with the highest aggregate tokens/second. 5. Only runs when the goal is a multi-user server deployment.

#### **Story 3.4: Benchmark Concurrency Level**
* As a Researcher (Randy), I want the benchmark tool to test various `--parallel` processing levels so that I can find the sweet spot for processing multiple sequences at once, maximizing my hardware utilization.
* **ACs:** 1. Tests different integer values for `--parallel`. 2. Measures throughput with multiple simultaneous sequences. 3. Identifies the best-performing `--parallel` value. 4. Uses optimal settings from previous phases as a baseline.

#### **Story 3.5: Display Real-time Benchmark Progress**
* As a Hobbyist (Tuning Tina), I want the tool to provide clear and continuous progress updates during the benchmark so that I know what it's testing and can estimate the time remaining.
* **ACs:** 1. Displays the current benchmark phase. 2. Shows the current step within the phase. 3. Displays a progress bar or percentage. 4. Shows key performance results in real-time.

### **Epic 4: Output and Reporting**

#### **Story 4.1: Output Final Command for Execution**
* As a Novice (Plug-and-Play Paul), I want the tool's primary output to be the complete and valid `llama-server` command so that I can copy and paste it directly into my terminal to run the server.
* **ACs:** 1. Default output is only the command string. 2. Command includes all optimal parameters. 3. Command is syntactically correct.

#### **Story 4.2: Provide Human-Readable Summary with Verbose Flag**
* As a Hobbyist (Tuning Tina), I want to use a `--verbose` flag to get a human-readable summary explaining the recommended settings so that I can understand the rationale behind the tool's choices.
* **ACs:** 1. `--verbose` flag outputs a formatted summary. 2. Summary includes detected hardware. 3. Summary explains key decisions and performance results.

#### **Story 4.3: Provide Machine-Readable JSON Output**
* As a Researcher (Randy), I want to use a `--json` flag to get the results in a machine-readable JSON format so that I can easily integrate the tool into my automated testing and research scripts.
* **ACs:** 1. `--json` flag outputs a single, valid JSON object. 2. JSON object contains all system info, model info, results, and the final command. 3. JSON schema is stable.

### **Epic 5: User Experience & Workflow**

#### **Story 5.1: Cache Benchmark Results for Instant Recommendations**
* As a Researcher (Randy), I want the tool to cache the results of a benchmark so that on subsequent runs with the same model and hardware, I get the optimal settings instantly without re-running the time-consuming benchmark.
* **ACs:** 1. Saves successful benchmark results to a local cache file. 2. Cache key is based on a unique hardware/model signature. 3. On subsequent runs, checks for a valid cache entry and uses it if found. 4. A `--force` flag allows the user to bypass the cache.

#### **Story 5.2: Preview Benchmark Plan with Dry Run**
* As a Hobbyist (Tuning Tina), I want to use a `--dry-run` flag with the benchmark command so that I can see a summary of the steps the tool plans to take without actually executing the full benchmark.
* **ACs:** 1. No performance tests are executed with `--dry-run`. 2. Prints a human-readable plan of the benchmark phases. 3. Output estimates the number of steps for each phase. 4. Exits gracefully after displaying the plan.

### **Epic 6: Server Hardening & Deployment**

#### **Story 6.1: Recommend API Key for Server Security**
* As an Advanced User (Researcher Randy) deploying a public-facing server, I want the tool to automatically include a recommendation for an API key in the generated command so that my server endpoint is secured by default.
* **ACs:** 1. Adds `--api-key` parameter for server use cases. 2. Uses a clear placeholder like 'YOUR_SECRET_KEY_HERE'. 3. Verbose output explains the importance of setting a strong key.

#### **Story 6.2: Recommend Request Timeout for Server Stability**
* As an Advanced User (Researcher Randy), I want the tool to suggest a reasonable request `--timeout` value in the generated command so that my server is protected from long-running or stalled requests.
* **ACs:** 1. Includes `--timeout` with a sensible default (e.g., 600). 2. Verbose output explains the purpose of the timeout. 3. Only active when generating a server command.

## **6. Non-Functional Requirements**
* **Platform Compatibility:** Buildable and runnable on Linux, macOS (Metal), and Windows.
* **Performance:** Analysis/generation should be fast (< 1 minute). Benchmark should aim for 5-15 minutes.
* **Usability:** Intuitive CLI with a clear `--help` message.
* **Reliability:** Handle errors gracefully and be robust to child process crashes, interpreting OOM failures as a hardware limit.

## **7. Out of Scope (for V1)**
* Graphical User Interface (GUI).
* Automatic model downloading.
* LoRA / Model Merging support.