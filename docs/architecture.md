# **llama-tune Architecture Document**

## **Introduction**

This document outlines the overall project architecture for `llama-tune`. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

### **Starter Template or Existing Project**

N/A - Greenfield project.

### **Change Log**

| Date       | Version | Description                 | Author  |
| :--------- | :------ | :-------------------------- | :------ |
| 2025-06-23 | 1.0     | Initial architecture design | Winston |

## **High Level Architecture**

### **Technical Summary**

The `llama-tune` project is a modular, command-line interface (CLI) tool built in Python. Its primary purpose is to act as an intelligent wrapper for the `llama.cpp` suite of executables (`llama-server`, `llama-bench`, `llama-gguf`). The architecture is centered around distinct modules for hardware analysis, model inspection, automated benchmarking, and command generation. It will execute the `llama.cpp` tools as external child processes to gather data, ensuring `llama-tune` remains decoupled from their core C++ codebase. The design prioritizes accuracy, user experience through an optional interactive wizard, and performance via result caching.

### **High Level Overview**

* **Architectural Style**: **Modular Monolith CLI**. The entire tool will be distributed as a single application, but its internal codebase will be strictly separated into modules for testability and maintainability.
* **Repository Structure**: **Monorepo**. All code for the application and its modules will reside in a single repository.
* **Key Decisions & Rationale**:
    * **External Process Execution**: `llama-tune` will call the user's `llama-server`, `llama-bench`, and `llama-gguf` executables as separate child processes. This de-couples our tool from the core `llama.cpp` project, making it flexible and easy to maintain as `llama.cpp` evolves.
    * [cite_start]**Result Caching**: The system will cache benchmark results to significantly improve user experience on subsequent runs, directly addressing `USR-501` from the PRD. [cite: 92]

### **High Level Project Diagram**

```mermaid
graph TD
    subgraph User Interaction
        U[User]
    end

    subgraph llama-tune CLI
        C[CLI Interface - Typer] --> D{Mode?}
        D -->|--interactive| W[Interactive Wizard]
        D -->|--benchmark| B[Benchmarking Engine]
        D -->|Default| B
    end
    
    subgraph Core Modules
        A[Hardware/Model Analyzer]
        CACHE[Cache]
        R[Output Generator]
    end

    subgraph External Processes
        LG[llama-gguf Process]
        LB[llama-bench Process]
        LS[llama-server Process]
    end

    U --> C
    W --> R
    
    B --> A
    A -- uses --> LG
    A --> B
    
    B --> CACHE
    CACHE --> B
    B -- runs for raw speed --> LB
    B -- runs for throughput --> LS
    LB -- performance data --> B
    LS -- performance data --> B
    B --> R
    R --> U
````

### **Architectural and Design Patterns**

  * **Modular Monolith:** Balances the simplicity of a single deployable artifact with the organizational benefits of separated, testable modules.
  * **Strategy Pattern:** To manage the different modes of operation (`--interactive`, `--benchmark`), encapsulating each workflow.
  * **Facade Pattern:** The `Hardware & Model Analyzer` will serve as a facade, providing a simple interface to complex, OS-specific detection logic and `llama-gguf` process calls.
  * [cite\_start]**Caching Pattern:** To store the results of computationally expensive benchmark operations, improving performance on repeated runs. [cite: 92]

## **Tech Stack**

### **Technology Stack Table**

| Category         | Technology | Version | Purpose                        | Rationale                                                                        |
| :--------------- | :--------- | :------ | :----------------------------- | :------------------------------------------------------------------------------- |
| **Language** | Python     | 3.11+   | Core application language      | User preference; excellent ecosystem for scripting and system interaction.      |
| **Runtime** | Python     | 3.11+   | The environment the code runs in | Using a recent, stable version provides modern features and long-term support.   |
| **Framework** | Typer      | latest  | CLI framework                  | Agreed upon previously; modern, intuitive, and powerful due to its `Click` foundation. |
| **Testing** | pytest     | latest  | Testing framework              | The de-facto standard for Python testing; its fixture system is excellent for all tiers of our testing strategy. |
| **Dependency Mgmt** | Poetry     | latest  | Dependency & package management | A modern tool that provides dependency locking for reproducible builds and simplifies virtual environment management. |
| **Build Tool** | Poetry     | latest  | Application Packaging          | Poetry has robust, built-in capabilities for building and publishing packages to PyPI. |

## **Data Models**

### **SystemProfile**

  * **Purpose:** To hold all detected hardware specifications of the user's machine.
  * **Key Attributes:** `cpu_cores: int`, `total_ram_gb: float`, `gpus: List[GpuInfo]`, `numa_detected: bool`, `blas_backend: str`
  * **Relationships:** Contains a list of `GpuInfo` models.

### **GpuInfo**

  * **Purpose:** To store the specifications of a single detected GPU.
  * **Key Attributes:** `model_name: str`, `vram_gb: float`
  * **Relationships:** Is contained within the `SystemProfile` model.

### **ModelProfile**

  * **Purpose:** To hold all key metadata extracted from the user's GGUF model file.
  * **Key Attributes:** `file_path: str`, `architecture: str`, `layer_count: int`, `quantization_type: str`
  * **Relationships:** Primary input data model.

### **BenchmarkResult**

  * **Purpose:** To store the performance metrics from a single benchmark run.
  * **Key Attributes:** `n_gpu_layers: int`, `prompt_speed_tps: float`, `generation_speed_tps: float`, `batch_size: Optional[int]`, `parallel_level: Optional[int]`
  * **Relationships:** A list of these is analyzed to find the `OptimalConfiguration`.

### **OptimalConfiguration**

  * **Purpose:** The final, comprehensive data object representing the tool's complete output. This structure is saved to the cache.
  * **Key Attributes:** `system_profile: SystemProfile`, `model_profile: ModelProfile`, `best_benchmark_result: BenchmarkResult`, `generated_command: str`, `notes: List[str]`
  * **Relationships:** The top-level data model that aggregates all other information.

## **Components**

### **1. CLI Interface**

  * **Responsibility:** The main entry point; parses CLI arguments and delegates control.
  * **Key Interfaces:** `main(args)`
  * **Dependencies:** `Interactive Wizard`, `Benchmarking Engine`
  * **Technology Stack:** Python, Typer

### **2. Hardware & Model Analyzer (Revised)**

  * **Responsibility:** To detect system hardware and parse GGUF model metadata by executing the `llama-gguf` utility and parsing its output.
  * **Key Interfaces:** `get_system_profile() -> SystemProfile`, `get_model_profile(path: str) -> ModelProfile`
  * **Dependencies:** External `llama-gguf` executable.
  * **Technology Stack:** Python

### **3. Interactive Wizard**

  * **Responsibility:** To orchestrate the guided question-and-answer session.
  * **Key Interfaces:** `run_wizard() -> OptimalConfiguration`
  * **Dependencies:** `Hardware & Model Analyzer`, `Output Generator`
  * **Technology Stack:** Python

### **4. Benchmarking Engine**

  * **Responsibility:** To run the multi-phase benchmark, calling `llama-bench` and `llama-server` as external processes and analyzing their performance.
  * **Key Interfaces:** `run_benchmark(model: ModelProfile, system: SystemProfile) -> BenchmarkResult`
  * **Dependencies:** `Hardware & Model Analyzer`, `Cache Manager`, external `llama-bench`/`llama-server` executables.
  * **Technology Stack:** Python (`subprocess` module).

### **5. Cache Manager**

  * **Responsibility:** To save and retrieve `OptimalConfiguration` objects to/from a local file-based cache.
  * **Key Interfaces:** `get_from_cache(key: str) -> Optional[OptimalConfiguration]`, `save_to_cache(key: str, config: OptimalConfiguration)`
  * **Dependencies:** None.
  * **Technology Stack:** Python (`pathlib`, `json`).

### **6. Output Generator**

  * **Responsibility:** To format the final `OptimalConfiguration` object for display based on the output mode (`--verbose`, `--json`, or default).
  * **Key Interfaces:** `generate_output(config: OptimalConfiguration, mode: str)`
  * **Dependencies:** None.
  * **Technology Stack:** Python.

## **External APIs**

Not Applicable. This is a local CLI tool with no required external network API calls.

## **Core Workflows**

### **Workflow 1: Automated Benchmark (`--benchmark`)**

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant BenchmarkingEngine as BE
    participant CacheManager as CM
    participant Analyzer
    participant OutputGenerator as OG
    actor Ext_Processes as "llama-bench/server"

    User->>CLI: runs `llama-tune --benchmark ...`
    CLI->>BE: run_benchmark()
    BE->>CM: get_from_cache(key)
    
    alt Cache Miss
        CM-->>BE: null
        BE->>Analyzer: get_system_profile(), get_model_profile()
        Analyzer-->>BE: SystemProfile, ModelProfile
        
        loop Benchmark Phases
            BE->>Ext_Processes: execute(params)
            Ext_Processes-->>BE: performance data
        end

        BE->>CM: save_to_cache(key, results)
        BE->>OG: generate_output(results)
        OG-->>CLI: formatted string
        CLI-->>User: prints command & summary
    else Cache Hit
        CM-->>BE: OptimalConfiguration
        BE->>OG: generate_output(cached_config)
        OG-->>CLI: formatted string
        CLI-->>User: prints command & summary
    end
```

### **Workflow 2: Interactive Wizard (`--interactive`)**

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant InteractiveWizard as WIZ
    participant Analyzer
    participant OutputGenerator as OG

    User->>CLI: runs `llama-tune --interactive ...`
    CLI->>WIZ: run_wizard()
    WIZ->>User: "Choose Use Case..."
    User-->>WIZ: Selection (e.g., "Chatbot")
    WIZ->>User: "Enter Context Size..."
    User-->>WIZ: Value (e.g., 4096)

    WIZ->>Analyzer: get_system_profile(), get_model_profile()
    Analyzer-->>WIZ: SystemProfile, ModelProfile

    WIZ->>OG: generate_output(config_from_choices)
    OG-->>CLI: formatted string
    CLI-->>User: prints command & summary
```

## **REST API Spec**

Not Applicable.

## **Database Schema**

Not Applicable.

## **Source Tree**

```plaintext
llama-tune/
├── .github/
│   └── workflows/              # CI/CD pipelines (e.g., for testing & publishing)
│       └── release.yml
├── docs/                       # Project documentation
│   ├── prd.md
│   └── architecture.md
├── src/
│   └── llama_tune/             # Main application package
│       ├── __init__.py
│       ├── analyzer/             # Hardware & Model Analyzer component
│       ├── benchmarker/          # Benchmarking Engine component
│       ├── caching/              # Cache Manager component
│       ├── cli.py                # Main CLI Interface (Typer app)
│       ├── core/                 # Shared core logic, including Data Models
│       ├── reporting/            # Output Generator component
│       └── wizard/               # Interactive Wizard component
├── tests/                      # All tests
│   ├── e2e/                    # End-to-end tests for the CLI
│   ├── integration/            # Integration tests for component interactions
│   └── unit/                   # Unit tests for individual modules
├── .gitignore                  # Git ignore file
├── pyproject.toml              # Poetry configuration and dependencies
└── README.md                   # Project README
```

## **Infrastructure and Deployment**

This project will be distributed as a Python package on PyPI.

  * **Packaging**: **Poetry** will be used to build the package into a distributable format.
  * **CI/CD Pipeline**: A **GitHub Actions** pipeline will be configured to automatically test, build, and publish the package to PyPI whenever a new version tag is pushed to the `main` branch.
  * **End-User Installation**: The user will install the tool via `pip install llama-tune`.
  * **Prerequisite**: This workflow requires a PyPI account and an API token to be configured as a secret in the GitHub repository.

## **Error Handling Strategy**

  * **General**: The tool will use custom exceptions inheriting from a base `LlamaTuneError` for clear, specific error handling. Unhandled errors will be caught at the top level, and a user-friendly message will be printed to `stderr`.
  * **Logging**: Python's built-in `logging` module will be used. A `--debug` flag will enable verbose logging. Logs go to `stderr` to separate them from the primary output on `stdout`.
  * **External Processes**: Failures in external processes (`llama-bench`, etc.) will be caught gracefully. The process's `stderr` will be logged for debugging.

## **Coding Standards**

  * **Formatting & Linting**: Code will be formatted with **Black** and linted with **Ruff**.
  * **Type Hinting**: All function signatures must include type hints.
  * **Naming**: Standard PEP 8 conventions will be followed (`snake_case` for functions/variables, `PascalCase` for classes).
  * **Critical Rules**: All external process calls must be centralized in their respective components; Data Models must be used for data transfer between components; specific exceptions must be caught; all user-facing output must be handled by the `Output Generator`.

## **Test Strategy and Standards**

  * **Philosophy**: A "test-after" approach will be used with a target of 85% code coverage, enforced by the CI pipeline.
  * **Test Types**:
      * **Unit Tests (`tests/unit/`)**: To test functions in isolation, using `pytest` and `pytest-mock`.
      * **Integration Tests (`tests/integration/`)**: To test interactions between internal components.
      * **E2E Tests (`tests/e2e/`)**: To test the full CLI command, using mocked external processes.
  * **Continuous Testing**: The GitHub Actions pipeline will run the full `pytest` suite on every pull request and push, blocking merges on failure.

## **Security**

  * **Input Validation**: `Typer` will be used for basic type validation; manual validation will be added for file paths.
  * **Data Protection**: The local cache will not store any personally identifiable information (PII).
  * **Dependency Security**: **GitHub's Dependabot** will be used to automatically scan for and create pull requests for vulnerable dependencies.
  * **Security Testing**: The **Bandit** static analysis tool will be integrated into the CI pipeline to scan for common security issues in the Python code.

## **Next Steps**

The project is ready to move into the development cycle in an IDE, starting with story implementation.
