# Story 3.7: Generate and Report Pareto Optimal Front

## Status: Draft

## Story

-   **As a** Researcher (<PERSON>),
-   **I want** the benchmark results to be analyzed and presented as a Pareto optimal front,
-   **so that** I can easily identify the set of most efficient configurations and understand the trade-offs between key performance metrics.

## Acceptance Criteria (ACs)

1.  A new `analysis` module (e.g., `src/llama_tune/analysis/pareto_analyzer.py`) will be created that takes the statistical data from Story 3.6 as input.
2.  The module will implement a Pareto front algorithm to identify the set of non-dominated configurations based on at least three competing objectives: maximizing `generation_speed_tps`, maximizing `prompt_speed_tps`, and minimizing `n_gpu_layers`.
3.  The final CLI report (via `OutputGenerator`) will be enhanced to include a dedicated "Pareto Optimal Configurations" section, listing the configurations on the front.
4.  A new CLI flag, `--plot`, will generate an interactive HTML file using `Plotly` containing a scatter plot visualizing the Pareto front. The axes should represent key trade-offs (e.g., `n_gpu_layers` vs. `generation_speed_tps`).
5.  The unit tests for the analysis module must validate the correctness of the Pareto front calculation with known data sets.

## Tasks / Subtasks

-   [ ] **Task 1 (AC: 1, 2):** Create a new `analysis` module with a function to calculate the Pareto front.
-   [ ] **Task 2 (AC: 3):** Update the reporting function in `cli.py` to display the Pareto optimal set in a table.
-   [ ] **Task 3 (AC: 4):** Add the `--plot` flag and integrate a plotting library (e.g., Plotly) to generate the HTML visualization.
-   [ ] **Task 4 (AC: 5):** Create comprehensive unit tests for the Pareto front algorithm.

## Dependencies

-   This story is dependent on Story 3.6: "Implement Statistically Significant Benchmark Measurements" being completed, as it requires the statistical data generated by that story.
