# Story 1.4: Read Model Metadata

## Status: Approved

## Story

- As a <PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>-<PERSON>)
- I want the tool to automatically read the metadata from my GGUF model file
- so that it understands the model's properties without me having to specify them manually.

## Acceptance Criteria (ACs)

1. Given a path to a model, the tool successfully parses the GGUF file to extract its metadata.
2. Extracted metadata includes at least the model architecture (e.g., Llama), total layer count, and quantization type (e.g., Q4_K_M).
3. The extracted total layer count is used as the upper bound for the `--n-gpu-layers` calculation.
4. If the provided file is not a valid GGUF model, the tool exits with a clear error message.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 2): In the `analyzer` component, implement a function `get_model_profile(path: str)`.
- [ ] Task 2 (AC: 1, 2): This function must call the `llama-gguf` executable as an external process, passing the model file path as an argument.
- [ ] Task 3 (AC: 2): Parse the output of the `llama-gguf` command to extract the required metadata fields (architecture, layer_count, quantization_type).
- [ ] Task 4 (AC: 4): Implement error handling for cases where the `llama-gguf` command fails or reports that the file is not a valid GGUF model.
- [ ] Task 5 (AC: 1, 2, 4): Write unit tests for `get_model_profile()` that use mock `llama-gguf` output from a fixture file for both success and failure scenarios.
- [ ] Task 6 (AC: 3): Ensure the `ModelProfile` data model is correctly populated and returned.

## Dev Notes

As per our architecture, this should be done by calling the external `llama-gguf` process and parsing its output, not by implementing a new GGUF parser in Python. The extracted `layer_count` is a critical dependency for the GPU offload calculations in other stories.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/analyzer/`), coverage requirement: 85%
- [ ] Integration Test
- [ ] E2E Test

**Manual Test Steps**: Run `llama-tune --verbose --model /path/to/model.gguf` and verify the summary correctly displays the model's architecture, layer count, and quantization type.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLLLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |