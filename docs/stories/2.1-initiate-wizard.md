# Story 2.1: Initiate Interactive Wizard

## Status: Approved

## Story

- As a <PERSON><PERSON> (<PERSON><PERSON>-and-<PERSON>)
- I want to start the tool with an `--interactive` flag to launch a guided setup wizard
- so that I can get a working configuration without needing to know all the technical parameters.

## Acceptance Criteria (ACs)

1. When the `llama-tune` command is run with the `--interactive` flag, a step-by-step interactive session begins.
2. The wizard prints a clear welcome message explaining its purpose.
3. If the `--interactive` flag is not provided, the wizard does not run.
4. The interactive prompts are clear, easy to understand, and guide the user through the configuration process.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 3): In `src/llama_tune/cli.py`, add a Typer option for `--interactive` that acts as a boolean flag.
- [ ] Task 2 (AC: 1): In the main CLI function, add logic to call the `InteractiveWizard` component if the `--interactive` flag is present.
- [ ] Task 3 (AC: 2): Create the initial `InteractiveWizard` component in `src/llama_tune/wizard/`. Its main method should print a welcome message.
- [ ] Task 4 (AC: 1, 3): Write an E2E test to verify that running `llama-tune --interactive` prints the welcome message, and running without the flag does not.

## Dev Notes

This story sets up the entry point for the entire interactive experience. The wizard logic should be fully contained within the `wizard` component. For the Text-based User Interface (TUI), consider using a library like `rich` or `questionary` to create user-friendly prompts; add it as a dependency using Poetry.

### Testing

Dev Note: Story Requires the following tests:
- [ ] `pytest` Unit Tests
- [ ] Integration Test
- [x] E2E Test: A simple E2E test to invoke the `--interactive` flag and assert that the wizard's welcome message is printed.

**Manual Test Steps**: Run `llama-tune --interactive` and verify the wizard starts. Run `llama-tune` without the flag and verify it does not.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |