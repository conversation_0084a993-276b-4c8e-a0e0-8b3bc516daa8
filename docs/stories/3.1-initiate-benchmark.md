# Story 3.1: Initiate Automated Benchmark

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want to run the tool with a `--benchmark` flag and specify a target context size
- so that I can automatically find the most performant settings for my specific hardware and model.

## Acceptance Criteria (ACs)

1. When the `llama-tune` command is run with the `--benchmark` flag, the automated benchmarking process is initiated.
2. The benchmark process requires the user to specify a model file and a target `--ctx-size` to test against.
3. If the `--benchmark` flag is provided without a required argument (like the model file), the tool exits with a clear error message.
4. The tool executes the full hardware and model analysis (as defined in Epic 1) as a prerequisite before starting the benchmark.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 2): In `src/llama_tune/cli.py`, add a `--benchmark` flag and ensure that arguments for `--model` and `--ctx-size` are also present when it's used.
- [ ] Task 2 (AC: 3): Implement validation logic in the CLI to check for the presence of required arguments and exit with a helpful error message if they are missing.
- [ ] Task 3 (AC: 1): If validation passes, the main CLI function should call the `BenchmarkingEngine` component's main `run_benchmark()` method.
- [ ] Task 4 (AC: 4): The `run_benchmark()` method must begin by calling the `Analyzer` component to get the `SystemProfile` and `ModelProfile`.
- [ ] Task 5 (AC: 3): Write an E2E test that invokes `llama-tune --benchmark` without the `--model` argument and asserts that the application exits with the correct error message.

## Dev Notes

This story sets up the primary user flow for advanced users. The `BenchmarkingEngine` will become the main orchestrator for this complex workflow. All logic should be delegated to it from the `cli.py` entry point to keep the CLI logic simple.

### Testing

Dev Note: Story Requires the following tests:
- [ ] `pytest` Unit Tests
- [ ] Integration Test
- [x] E2E Test: Test the CLI argument parsing, including the failure case where required arguments are missing when using the benchmark flag.

**Manual Test Steps**: Run `llama-tune --benchmark` without specifying a model and verify that it produces a clear error message and exits.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |