# Story 2.3: Specify Target Context Size in Wizard

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want the interactive wizard to ask for my desired context size
- so that I can control the trade-off between memory usage and the model's conversational history length.

## Acceptance Criteria (ACs)

1. The wizard prompts the user to enter a value for the target context size (`--ctx-size`).
2. The prompt suggests a sensible default value (e.g., `2048`) that the user can accept or override.
3. The user's input is validated to ensure it is a positive integer.
4. The final `--ctx-size` value is used in the feasibility check and included in the generated command.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 2): In the `InteractiveWizard` component, create a step that prompts the user for a numeric input for the context size.
- [ ] Task 2 (AC: 2): The prompt should display a default value (e.g., 2048).
- [ ] Task 3 (AC: 3): Use input validation to ensure the user's entry is a positive integer. Re-prompt if the input is invalid.
- [ ] Task 4 (AC: 4): Store the validated context size value in the wizard's state for use in the final command generation.
- [ ] Task 5 (AC: 3): Write unit tests to verify that the input validation correctly handles both valid and invalid entries.

## Dev Notes

The context size has a significant impact on memory usage. This value will need to be available to the `Feasibility Check` logic (from Story 1.5) to ensure the user doesn't choose a context size their machine can't handle.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/wizard/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Use mocking to simulate a user entering a valid number, and another test for entering invalid input (like text) to ensure the validation works as expected.

**Manual Test Steps**: Run `llama-tune --interactive`. At the context size prompt, enter a valid number and confirm it proceeds. Rerun and enter non-numeric text to verify it prompts for a valid number again.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |