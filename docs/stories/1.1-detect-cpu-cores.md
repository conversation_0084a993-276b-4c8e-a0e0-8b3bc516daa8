# Story 1.1: Detect Physical CPU Cores for Optimal Thread Count

## Status: Approved

## Story

- As an Advanced User (<PERSON><PERSON>)
- I want the tool to automatically detect the number of physical CPU cores on my system
- so that an optimal `--threads` value is recommended in the final command for maximum performance.

## Acceptance Criteria (ACs)

1. The tool correctly identifies the number of physical (not logical/hyper-threaded) CPU cores.
2. The generated command includes the `--threads` argument with a value matching the detected physical core count.
3. The detection mechanism is confirmed to work on Linux, Windows, and macOS.
4. If for any reason the core count cannot be determined, the `--threads` argument is omitted from the command, and a warning is noted in the `--verbose` output.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 3): Implement a function `get_physical_cpu_cores()` within the `analyzer` component.
  - [ ] Subtask 1.1: Add logic for Linux.
  - [ ] Subtask 1.2: Add logic for Windows.
  - [ ] Subtask 1.3: Add logic for macOS.
- [ ] Task 2 (AC: 1, 3, 4): Write unit tests for `get_physical_cpu_cores()`, covering all three operating systems and failure cases.
- [ ] Task 3 (AC: 2, 4): Integrate the `get_physical_cpu_cores()` result into the `SystemProfile` data model and ensure the `Output Generator` component uses it to construct the final command.

## Dev Notes

The implementation for this story should be contained within the `analyzer` component, and the final value exposed through the `SystemProfile` data model as defined in `architecture.md`. All code must adhere to the defined Coding Standards, especially regarding Type Hinting.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/analyzer/`), coverage requirement: 85%
- [ ] Integration Test
- [ ] E2E Test

**Manual Test Steps**: Run `llama-tune` on machines with different CPU configurations and verify the `--threads` value in the output command is correct.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |