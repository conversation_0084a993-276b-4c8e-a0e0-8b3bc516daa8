# Story 4.3: Provide Machine-Readable JSON Output

## Status: Approved

## Story

- As a Researcher (<PERSON>)
- I want to use a `--json` flag to get the results in a machine-readable JSON format
- so that I can easily integrate the tool into my automated testing and research scripts.

## Acceptance Criteria (ACs)

1. When the `--json` flag is used, the tool's entire standard output is a single, valid JSON object.
2. The JSON object contains all detected system information, model metadata, benchmark results, and the final generated `llama-server` command as a field within the object.
3. The JSON schema is stable and well-defined, allowing for reliable parsing by other programs.
4. The tool should not print any other text (including logs or progress bars) to standard output when `--json` is active to ensure clean parsing.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In `src/llama_tune/cli.py`, add a Typer option for `--json` that acts as a boolean flag.
- [ ] Task 2 (AC: 1, 2): In the `Output Generator` component, create a method to serialize the `OptimalConfiguration` data model object into a JSON string.
- [ ] Task 3 (AC: 3): Ensure all nested data models are JSON serializable. This may require custom encoders for complex types like `datetime` if they are added later.
- [ ] Task 4 (AC: 1, 4): The main `CLI` component should call this method and print *only* the resulting JSON string to `stdout` when the `--json` flag is present.
- [ ] Task 5 (AC: 2, 3): Write unit tests to verify that a sample `OptimalConfiguration` object is correctly serialized into a valid JSON string with the expected schema.

## Dev Notes

The JSON output should be the *only* thing printed to `stdout` when `--json` is active to ensure it can be reliably piped to other tools like `jq`. All logging and progress indicators should be directed to `stderr` so they don't interfere. The structure of this JSON output should be treated as a public API; changes to it in the future would be a breaking change for scripting users.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/reporting/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Run a benchmark with a mocked backend and the `--json` flag, then parse the `stdout` as JSON and assert that it contains the correct structure and data.

**Manual Test Steps**: Run `llama-tune --benchmark --json` and pipe the output to a tool like `jq` to verify it's valid, well-formed JSON.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |