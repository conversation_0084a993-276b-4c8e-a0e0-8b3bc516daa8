# Story 3.6: Implement Statistically Significant Benchmark Measurements

## Status: Draft

## Story

-   **As a** Researcher (<PERSON>),
-   **I want** the benchmarking engine to run each configuration multiple times and calculate basic statistics,
-   **so that** I can base my performance decisions on reliable and repeatable data, not single-run anomalies.

## Acceptance Criteria (ACs)

1.  A new CLI flag, `--num-runs <N>`, will be added to the `benchmark` command, allowing the user to specify how many times each configuration should be tested. `<N>` must be an integer greater than or equal to `1`. It should default to `3`.
2.  The `_run_gpu_offload_benchmark` and `_run_throughput_benchmark` methods in the `BenchmarkingEngine` must be updated to loop through each configuration `--num-runs` times.
3.  The `BenchmarkResult` data model must be extended to store the results of all individual runs for each configuration.
4.  After completing all runs for a single configuration, the engine must calculate the **mean** and **standard deviation** for the following primary performance metrics: Prompt Processing Speed (t/s), Token Generation Speed (t/s), and Total Throughput (t/s). These statistics must be stored in the `BenchmarkResult` data model.
5.  The real-time progress display must be updated to show the current run number (e.g., "Run 3/5").
6.  The final data structure passed to the analysis phase must include these calculated statistics (mean, stddev) for each tested configuration.

## Tasks / Subtasks

-   [ ] **Task 1 (AC: 1):** Add `--num-runs` argument to the `benchmark` command in `cli.py`.
-   [ ] **Task 2 (AC: 2):** Modify `BenchmarkingEngine` to incorporate the multi-run loop for both GPU and throughput benchmarks.
-   [ ] **Task 3 (AC: 3, 4):** Update the `BenchmarkResult` data model and data collection logic to store all runs and compute mean and standard deviation.
-   [ ] **Task 4 (AC: 5):** Enhance the `ProgressCallback` and its implementation in `cli.py` to display the current run count.
-   [ ] **Task 5 (AC: 6):** Ensure the `OptimalConfiguration` data structure includes the new statistical measures and is passed to the reporting components.
-   [ ] **Task 6:** Add unit tests to validate the statistical calculation logic.
