# Story 3.3c: Implement E2E Test for Concurrent Throughput Benchmark

## Status: Draft

## Story

-   As a QA Engineer,
-   I want an E2E test for the concurrent throughput benchmark,
-   so that I can verify the end-to-end functionality and correctness of the benchmark.

## Acceptance Criteria (ACs)

1.  The E2E test should run the `llama-tune` CLI with the `--benchmark` flag and a mock model.
2.  The test should assert that the output contains the expected optimal configuration (e.g., `n_gpu_layers`, `batch_size`, `parallel_level`, `generation_speed_tps`).
3.  The test should verify that the `llama-server` subprocess is correctly started and stopped during the benchmark.
4.  The test should use a mock `llama-server` that provides predictable output for various `n_gpu_layers`, `batch_size`, and `parallel` settings.

## Tasks / Subtasks

-   [ ] Subtask 1.1: Create a mock `llama-server` executable or script that simulates its behavior.
-   [ ] Subtask 1.2: Implement the E2E test using `subprocess` to call the `llama-tune` CLI.
-   [ ] Subtask 1.3: Parse the CLI output to extract the benchmark results.
-   [ ] Subtask 1.4: Assert that the extracted results match the expected optimal configuration from the mock server.

## Dev Notes

Focus on creating a robust mock server that can simulate different performance characteristics and OOM conditions. The mock server should be able to respond to HTTP requests for completion and provide consistent output for testing purposes.
