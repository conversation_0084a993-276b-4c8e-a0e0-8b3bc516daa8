# Story 1.8: Detect and Enable High-Performance BLAS Backend

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want the tool to detect if `llama.cpp` was compiled with a high-performance BLAS backend
- so that it can recommend enabling it for a potential speed boost.

## Acceptance Criteria (ACs)

1. The tool can determine which BLAS (Basic Linear Algebra Subprograms) backend `llama.cpp` was compiled with.
2. If a high-performance backend like `cuBLAS` (for CUDA) or `OpenBLAS` (for CPU) is detected, the appropriate flag (e.g., `--use-cublas`) is added to the generated command.
3. If a standard or no special BLAS backend is detected, no related flag is added.
4. The `--verbose` output explains the BLAS-related recommendation.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In the `analyzer` component, implement a function `get_blas_backend()` that returns a string (e.g., "cuBLAS", "OpenBLAS", "None").
  - [ ] Subtask 1.1: The function should run a `llama.cpp` executable (like `llama-bench`) with a minimal command and capture its initial startup output, which often contains build information.
  - [ ] Subtask 1.2: Parse the captured text to identify keywords related to BLAS backends.
- [ ] Task 2 (AC: 1): Write unit tests for `get_blas_backend()` that use mocked process output from fixtures for different build configurations.
- [ ] Task 3 (AC: 2, 3): The `Output Generator` component must check the `system.blas_backend` property and add the correct flag (e.g., `--use-cublas`) to the final command if a high-performance backend was detected.
- [ ] Task 4 (AC: 4): The `Output Generator` should also add a note to the verbose output explaining the decision.

## Dev Notes

The most reliable way to detect the BLAS backend is likely by capturing and parsing the initial startup text from one of the `llama.cpp` executables. The implementation needs to be robust against cases where this information isn't present in the output.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/analyzer/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Create E2E tests using mocked `SystemProfile` objects with different `blas_backend` values and assert the final command contains (or does not contain) the correct flag.

**Manual Test Steps**: This is difficult to test without having multiple custom builds of `llama.cpp`. The primary verification will be through the automated tests with mocked process output.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |