# Story 3.3b: Enhance Benchmark with Statistical Analysis & Pareto Reporting

## Status: Draft

## Prerequisites: Story 3.3a must be complete.

## Story

-   As a Researcher (Randy),
-   I want the concurrent throughput benchmark to be enhanced with statistical analysis and multi-objective reporting,
-   so that I can have high confidence in the results and understand the optimal trade-offs between performance and resource consumption.

## Acceptance Criteria (ACs)

1.  For each parameter set tested in the benchmark, a minimum of three runs are performed.
2.  If the standard deviation of throughput for a parameter set exceeds 5% of the mean, additional runs are performed (up to a maximum of 10) until the result stabilizes.
3.  If results do not stabilize after 10 runs, a "High Variance" warning is included in the final report.
4.  The benchmark engine identifies the **Pareto-optimal front** of configurations, considering throughput, VRAM usage, and CPU usage.
5.  The `--verbose` output is updated to display multiple **categorized recommendations** from the Pareto front: "Max Throughput", "Max Efficiency", and "VRAM-Constrained".
6.  The `--json` output is enhanced to include the full benchmark dataset, including performance metrics and confidence intervals for every tested configuration.
7.  A new optional flag, `--prompts`, allows the user to provide a custom file of prompts for the workload.
8.  The tool provides clear in-flight feedback, such as notifying the user when additional runs are being performed for statistical stability.

## Tasks / Subtasks

-   [ ] **Task 1: Enhance `BenchmarkingEngine` Analysis**:
    -   [ ] Subtask 1.1: Implement the logic for multiple runs and the standard deviation stability check.
    -   [ ] Subtask 1.2: Implement the Pareto front filtering algorithm.
    -   [ ] Subtask 1.3: Implement the logic for categorizing the final recommendations.
-   [ ] **Task 2: Implement Resource Monitoring**:
    -   [ ] Subtask 2.1: Use `psutil` to capture CPU and VRAM usage during each benchmark run and associate it with the results.
-   [ ] **Task 3: Update `Client Simulator`**:
    -   [ ] Subtask 3.1: Add the logic to load and use prompts from a user-provided file via the `--prompts` flag.
-   [ ] **Task 4: Enhance `Output Generator`**:
    -   [ ] Subtask 4.1: Modify the `--verbose` report to show the categorized Pareto recommendations.
    -   [ ] Subtask 4.2: Modify the `--json` report to include the full raw data and calculated confidence intervals.
-   [ ] **Task 5: Update `CLI`**:
    -   [ ] Subtask 5.1: Add the `--prompts` flag and its documentation in the `--help` message.

## Dev Notes

This story builds directly upon the foundation of Story 3.3a. The focus here is less on the mechanics of testing and more on the mathematical and statistical rigor of the analysis. The implementation of the Pareto front logic is the most critical and complex part of this story.

### Testing

-   [x] **Unit Tests**:
    -   Crucially, test the Pareto front filtering logic with a variety of mock datasets to ensure its correctness.
    -   Test the statistical stability logic, including the circuit breaker for high-variance results.
-   [x] **Integration Test**: Test the flow of data from the `psutil` resource monitor into the final `BenchmarkResult` objects.
-   [x] **E2E Test**: Run the benchmark with the `--json` flag and validate that the output file contains the expected detailed structure, including the full dataset and Pareto front.