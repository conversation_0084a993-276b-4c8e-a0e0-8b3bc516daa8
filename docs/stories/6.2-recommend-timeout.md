# Story 6.2: Recommend Request Timeout for Server Stability

## Status: Approved

## Story

- As an Advanced User (Research<PERSON> Randy)
- I want the tool to suggest a reasonable request `--timeout` value in the generated command
- so that my server is protected from long-running or stalled requests.

## Acceptance Criteria (ACs)

1. The generated `llama-server` command includes the `--timeout` parameter with a sensible default value (e.g., 600 seconds).
2. The `--verbose` output explains that the timeout prevents requests from hanging indefinitely and that the user can adjust the value based on their needs.
3. This feature is only active when generating a server command.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 3): In the `Output Generator` component, modify the command generation logic to add the `--timeout` flag with a default value when the configuration is for a server use case.
- [ ] Task 2 (AC: 2): In the verbose output generation method, add a note explaining the purpose of the timeout setting.
- [ ] Task 3 (AC: 1, 3): Write unit tests to verify that the `--timeout` flag is correctly added with its default value for server-type configurations and omitted otherwise.

## Dev Notes

Similar to the API key recommendation, this is a 'best practice' feature to help users run more stable servers. The default value should be chosen to be long enough for most reasonable requests but short enough to prevent a server from being perpetually stalled by a single bad request.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/reporting/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Run the wizard, select a server use-case, and assert that the final command includes the `--timeout` flag with the default value.

**Manual Test Steps**: Run the interactive wizard and select a server preset. Verify the final command contains the `--timeout` flag.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |