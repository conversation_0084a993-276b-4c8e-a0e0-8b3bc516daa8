# Story 5.1: Cache Benchmark Results for Instant Recommendations

## Status: Approved

## Story

- As a Researcher (<PERSON>)
- I want the tool to cache the results of a benchmark
- so that on subsequent runs with the same model and hardware, I get the optimal settings instantly without re-running the time-consuming benchmark.

## Acceptance Criteria (ACs)

1. After a successful benchmark, the results (the `OptimalConfiguration` object) are saved to a local cache file.
2. The cache entry is associated with a unique signature derived from the specific hardware and model file.
3. On subsequent runs, if a valid cache entry for the current configuration is found, the tool presents the cached results immediately and does not re-run the benchmark.
4. A `--force` or `--no-cache` flag must be available to allow the user to bypass the cache and force a new benchmark.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 2): In the `caching` component, implement `save_to_cache` and `get_from_cache` methods.
- [ ] Task 2 (AC: 2): Implement a `generate_cache_key` function. This should create a deterministic key based on key system specs (e.g., GPU model, CPU cores) and a hash of the model file.
- [ ] Task 3 (AC: 1): The cache should be stored in a standard user-specific application data directory (e.g., `~/.cache/llama-tune`).
- [ ] Task 4 (AC: 3): The `BenchmarkingEngine` must call `get_from_cache` before starting a benchmark. If a result is returned, the engine should bypass the benchmark and return the cached result.
- [ ] Task 5 (AC: 1): After a new benchmark is successfully completed, the `BenchmarkingEngine` must call `save_to_cache`.
- [ ] Task 6 (AC: 4): In `cli.py`, add a `--force` / `--no-cache` flag that tells the `BenchmarkingEngine` to skip the cache check.
- [ ] Task 7 (AC: 2): Write unit tests for the `CacheManager`'s save, load, and key generation logic.

## Dev Notes

The cache key generation is the most critical part of this story. It must be deterministic and accurately reflect the configuration being tested. A good approach would be to concatenate key hardware specs and the SHA256 hash of the model file to create the key. We should also consider adding the `llama-tune` tool's own version to the cache key, so updating the tool automatically invalidates old caches.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/caching/`), coverage requirement: 85%
- [x] Integration Test: Test that the `BenchmarkingEngine` correctly interacts with the `CacheManager` to both read from and write to the cache.
- [x] E2E Test: Run a benchmark, then run it again immediately and assert that the second run is instantaneous and returns the same result. Then run it with `--force` and assert that it performs the full benchmark again.

**Manual Test Steps**: Run a benchmark. Run it a second time and confirm it returns instantly. Delete the cache file (e.g., in `~/.cache/llama-tune`) and run it again to confirm it performs a full benchmark.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |