# Story 2.2: Select Configuration Preset by Use Case

## Status: Approved

## Story

- As a <PERSON><PERSON> (<PERSON><PERSON>-and-Play <PERSON>)
- I want the interactive wizard to ask me for my intended use case
- so that it can apply optimized performance and text-generation (sampling) presets automatically.

## Acceptance Criteria (ACs)

1. The wizard presents the user with a numbered list of use cases: `1. Conversational Chatbot`, `2. RAG / Document Analysis`, `3. Code Completion`, `4. Custom / Max Performance`.
2. Based on the selection, the appropriate set of sampling parameters (e.g., temperature, repeat penalty, etc.) are chosen and added to the final command.
3. The "RAG / Document Analysis" preset ensures the benchmark prioritizes prompt processing speed.
4. The "Custom / Max Performance" option skips the application of any sampling presets.
5. The wizard confirms the user's selection before proceeding to the next step.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 5): In the `InteractiveWizard` component, implement a step that prompts the user with the four use-case options. A library like `questionary` is recommended for the selectable list.
- [ ] Task 2 (AC: 2): In a `core` module, create a data structure (e.g., a dictionary or enum) that maps each use case to a specific set of `llama-server` sampling parameters.
- [ ] Task 3 (AC: 2, 4): Store the user's selection and the corresponding set of sampling parameters in the wizard's state. If "Custom" is chosen, store an empty set.
- [ ] Task 4 (AC: 3): If "RAG / Document Analysis" is chosen, set a flag in the wizard's state to indicate that prompt processing speed should be prioritized in any subsequent benchmark.
- [ ] Task 5 (AC: 2): Write unit tests to verify that selecting each use case correctly retrieves the associated parameter set.

## Dev Notes

The specific sampling parameters for each preset (e.g., temperature, top_p, repeat_penalty) should be stored in a separate configuration file or a dedicated `core` module for easy modification in the future. This story is only concerned with the *selection* of the preset; the `Output Generator` will be responsible for applying these parameters to the final command string.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/wizard/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Use mocking to simulate a user selecting each of the options and assert that the wizard state is updated correctly.

**Manual Test Steps**: Run `llama-tune --interactive` and select each of the four options to ensure the flow continues as expected.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |