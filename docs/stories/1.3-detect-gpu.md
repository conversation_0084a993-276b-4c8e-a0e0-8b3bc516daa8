# Story 1.3: Detect GPU and VRAM for Optimal Offloading

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want the tool to automatically detect my GPU(s), including model name and VRAM capacity
- so that it can determine the maximum number of model layers to offload for the best performance.

## Acceptance Criteria (ACs)

1. The tool correctly identifies one or more compatible GPUs (e.g., NVIDIA, AMD, Apple Metal).
2. For each detected GPU, the tool reports its model name (e.g., "NVIDIA GeForce RTX 4070") and its total VRAM in GB.
3. The detected VRAM is used as the primary constraint when calculating the maximum possible value for `--n-gpu-layers`.
4. If no compatible GPU is found, the tool defaults to a CPU-only configuration.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 2): In the `analyzer` component, implement a function `get_gpu_info()` that returns a list of `GpuInfo` objects.
  - [ ] Subtask 1.1: Add logic to detect NVIDIA GPUs and VRAM (e.g., by parsing the output of `nvidia-smi`).
  - [ ] Subtask 1.2: Add logic to detect Apple Silicon (Metal) GPUs on macOS.
  - [ ] Subtask 1.3: Add logic to detect AMD GPUs (e.g., using ROCm-smi).
- [ ] Task 2 (AC: 4): Ensure the function returns an empty list if no compatible GPU is found or if the necessary query tools are not installed.
- [ ] Task 3 (AC: 1, 2, 4): Write unit tests for `get_gpu_info()` that mock the system command outputs for each GPU vendor and the no-GPU case.
- [ ] Task 4 (AC: 3): Integrate the result into the `SystemProfile` data model so it's available to other components like the `Benchmarking Engine`.

## Dev Notes

This is a potentially complex, multi-platform task. The implementation should be cleanly organized within the `analyzer` component. The function should be robust to cases where system tools like `nvidia-smi` are not installed or not in the system's PATH. This information is critical for Story 3.2.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/analyzer/`), coverage requirement: 85%
- [x] Integration Test: Verify that the main `get_system_profile()` function correctly populates the GPU info from this new function.
- [ ] E2E Test

**Manual Test Steps**: Run `llama-tune --verbose` on machines with different GPUs (NVIDIA, Apple Silicon, AMD) and on a machine with no GPU to verify the detection is accurate in all cases.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |