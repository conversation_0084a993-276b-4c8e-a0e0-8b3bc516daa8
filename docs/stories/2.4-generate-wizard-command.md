# Story 2.4: Generate and Display Final Command

## Status: Approved

## Story

- As a <PERSON><PERSON> (<PERSON><PERSON>-and-<PERSON>)
- I want the wizard to generate and display the complete `llama-server` command after I've answered all questions
- so that I can easily copy and run it to start the server.

## Acceptance Criteria (ACs)

1. After the final interactive prompt, the wizard compiles all detected hardware information (from Epic 1) and user choices (from Epic 2).
2. A complete and syntactically valid `llama-server` command is constructed.
3. The final command is printed clearly to the console, separate from other text, making it easy to copy.
4. The wizard session concludes with a confirmation message like "Configuration complete!".

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In the `InteractiveWizard` component, after the last prompt, gather all collected state (user choices, system info).
- [ ] Task 2 (AC: 1): Create an `OptimalConfiguration` data model object populated with this information.
- [ ] Task 3 (AC: 2, 3): Pass this `OptimalConfiguration` object to the `Output Generator` component.
- [ ] Task 4 (AC: 2, 3): The `Output Generator` should use the data to construct the final command string and return it to the `CLI` component for printing.
- [ ] Task 5 (AC: 4): The `CLI` component should print a concluding message after displaying the command.
- [ ] Task 6 (AC: 2): Write an integration test that runs the full wizard flow (with mocked prompts and analyzer data) and asserts that the final generated command string is correct.

## Dev Notes

This story ties the entire wizard flow together. The `InteractiveWizard`'s primary responsibility ends here by handing off the final configuration data to the `Output Generator`. The `Output Generator` is responsible for all the command formatting logic. This separation of concerns is important for maintainability.

### Testing

Dev Note: Story Requires the following tests:
- [ ] `pytest` Unit Tests
- [x] Integration Test: (location: `tests/integration/`) An integration test to verify the handoff from the `Wizard` to the `Analyzer` and finally to the `Output Generator` produces a correct command.
- [ ] E2E Test

**Manual Test Steps**: Run `llama-tune --interactive` through to completion and verify the output command is a valid, runnable command that reflects the choices made during the prompts.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |