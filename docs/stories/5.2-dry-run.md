# Story 5.2: Preview Benchmark Plan with Dry Run

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want to use a `--dry-run` flag with the benchmark command
- so that I can see a summary of the steps the tool plans to take without actually executing them.

## Acceptance Criteria (ACs)

1. When the benchmark command is run with the `--dry-run` flag, no actual performance tests are executed.
2. The tool prints a human-readable plan of the benchmark phases and the steps it would have taken.
3. The dry-run output should estimate the number of steps for each phase (e.g., "Phase 1: Will test up to 33 GPU layers. Phase 2: Will test 5 batching configurations.").
4. The tool exits with a status of 0 after displaying the dry-run plan.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In `src/llama_tune/cli.py`, add a Typer option for `--dry-run` that acts as a boolean flag.
- [ ] Task 2 (AC: 1): In the `BenchmarkingEngine`, if the `--dry-run` flag is active, the `run_benchmark` method must execute a separate "plan-only" logic path.
- [ ] Task 3 (AC: 2, 3): This planning path should calculate the number of steps for each benchmark phase without running any external processes.
- [ ] Task 4 (AC: 2, 3): The engine should return a data structure containing this plan.
- [ ] Task 5 (AC: 2, 3): The `Output Generator` will need a new method to format this plan into a human-readable summary.
- [ ] Task 6 (AC: 4): The `CLI` component will print the summary and then exit gracefully.
- [ ] Task 7 (AC: 2, 3): Write unit tests for the planning logic in the `BenchmarkingEngine`.

## Dev Notes

This feature provides transparency and improves the user experience. The logic should reuse as much of the benchmark setup as possible, simply skipping the actual execution of external processes. The output should be clear and set accurate expectations about what the benchmark will do.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/benchmarker/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Run `llama-tune --benchmark --dry-run` with a mock configuration and assert that the output contains the expected plan summary and that no benchmark is actually performed.

**Manual Test Steps**: Run `llama-tune --benchmark --dry-run` and verify that it quickly prints a summary of the planned benchmark steps and then exits without running a full benchmark.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |