# Story 3.2: Benchmark GPU Layer Offload and Throughput

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want the benchmark tool to systematically test different numbers of GPU layers (`--n-gpu-layers`)
- so that it can find the maximum number my VRAM can handle while measuring both prompt processing and token generation speed.

## Acceptance Criteria (ACs)

1. [cite_start]The benchmark iteratively increases the `--n-gpu-layers` value, starting from a reasonable baseline up to the model's maximum layer count. 
2. For each value, it runs a standardized test load to measure performance.
3. [cite_start]The tool must separately measure and record **prompt processing speed** (tokens/sec for the initial prompt) and **token generation speed** (tokens/sec for the response). 
4. [cite_start]If a test run fails (e.g., from an out-of-memory error), the tool must handle the crash gracefully, interpret it as the VRAM limit, and stop increasing the layer count. 
5. [cite_start]The process must identify the optimal `--n-gpu-layers` value that provides the best performance without instability. 

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In the `BenchmarkingEngine` component, create a method for the GPU offload benchmark phase.
- [ ] Task 2 (AC: 1, 2): This method should loop, incrementing the `--n-gpu-layers` value. Inside the loop, it must call the external `llama-bench` process with the current parameter set.
- [ ] Task 3 (AC: 3): Implement logic to parse the `stdout` from `llama-bench` to extract both prompt processing speed (pp) and token generation speed (tg).
- [ ] Task 4 (AC: 4): Wrap the subprocess call in error handling that can catch a crash and interpret it as the VRAM limit, which should gracefully end the loop.
- [ ] Task 5 (AC: 5): Store the performance results for each successful run. After the loop, analyze the results to find the configuration with the best performance.
- [ ] Task 6 (AC: 3): Write unit tests for the `llama-bench` output parsing logic using fixture files.

## Dev Notes

This is a core part of the application's logic. The interaction with the external `llama-bench` process must be very robust. [cite_start]Remember the architectural decision to use `llama-bench` for this raw performance test.  The loop should be intelligent; if performance starts to degrade significantly, it could decide to stop early.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/benchmarker/`), coverage requirement: 85%. Tests should cover parsing different mock `llama-bench` outputs.
- [x] Integration Test: Test the `BenchmarkingEngine`'s main method, providing it a mock `Analyzer` and asserting that it runs the correct sequence of benchmark phases.
- [ ] E2E Test

**Manual Test Steps**: Run `llama-tune --benchmark` on a GPU-enabled machine with a reasonably sized model. Check the verbose output to see the iterative results of the GPU offload test as it runs.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |