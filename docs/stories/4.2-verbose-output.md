# Story 4.2: Provide Human-Readable Summary with Verbose Flag

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want to use a `--verbose` flag to get a human-readable summary explaining the recommended settings
- so that I can understand the rationale behind the tool's choices.

## Acceptance Criteria (ACs)

1. When the `--verbose` flag is used, the tool outputs a formatted, easy-to-read summary in addition to the final command.
2. The summary must include detected hardware (CPU cores, RAM, GPU, VRAM).
3. The summary must explain key decisions, such as the chosen `--n-gpu-layers` and the final benchmarked performance in tokens/second.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In `src/llama_tune/cli.py`, add a Typer option for `--verbose` that acts as a boolean flag.
- [ ] Task 2 (AC: 1): In the `Output Generator` component, create a method to format the verbose summary string.
- [ ] Task 3 (AC: 2, 3): This method will take the `OptimalConfiguration` data model as input and use its properties (hardware info, benchmark results, notes) to build a formatted report.
- [ ] Task 4 (AC: 1): The main `CLI` component should call this method and print the summary *in addition* to the final command when the `--verbose` flag is present.
- [ ] Task 5 (AC: 2, 3): Write unit tests for the verbose summary formatting logic to ensure it correctly displays information from a sample `OptimalConfiguration` object.

## Dev Notes

The `rich` library, which we are considering for the wizard, would also be excellent for formatting this verbose output with colors, tables, and styles to make it easy to read. The `notes` field in the `OptimalConfiguration` data model should be used to populate the "rationale" part of the summary.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/reporting/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Run a benchmark with a mocked backend and the `--verbose` flag, then assert that the `stdout` contains both the final command and the expected summary sections.

**Manual Test Steps**: Run `llama-tune --benchmark --verbose` and check that the output includes a readable summary of the detected hardware and benchmark results.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |