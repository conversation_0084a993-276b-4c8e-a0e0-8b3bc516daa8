# Story 1.2: Detect System RAM for Memory-Aware Configuration

## Status: Approved

## Story

- As a Researcher (<PERSON>)
- I want the tool to detect the total amount of system RAM
- so that it can make informed decisions about model loading and memory locking (`--mlock`).

## Acceptance Criteria (ACs)

1. The tool accurately detects the total physical RAM installed in the system, reporting it in Gigabytes (GB).
2. The detected RAM value is used as a key input for the feasibility check (USR-105) and the `--mlock` recommendation (USR-107).
3. The detection mechanism functions correctly on Linux, Windows, and macOS.
4. If total RAM cannot be determined, a warning is displayed, and subsequent memory-dependent checks are skipped.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 3): In the `analyzer` component, implement a new function `get_total_ram_gb()`.
  - [ ] Subtask 1.1: Use a cross-platform library like `psutil` to fetch total physical memory.
  - [ ] Subtask 1.2: Convert the value from bytes to gigabytes.
- [ ] Task 2 (AC: 1, 3, 4): Write unit tests for `get_total_ram_gb()` that mock the underlying library calls for each OS and test the failure case.
- [ ] Task 3 (AC: 2): Integrate the new function into the `Analyzer`'s `get_system_profile()` interface so the result is added to the `SystemProfile` data model.

## Dev Notes

This function should be part of the `analyzer` component as defined in `architecture.md`. The PRD specifies that this information is a prerequisite for stories 1.5 and 1.7, so ensure the data is populated correctly in the `SystemProfile` data model for those future tasks.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/analyzer/`), coverage requirement: 85%
- [ ] Integration Test
- [ ] E2E Test

**Manual Test Steps**: Run `llama-tune --verbose` on different machines and confirm the detected RAM value in the summary is accurate.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |