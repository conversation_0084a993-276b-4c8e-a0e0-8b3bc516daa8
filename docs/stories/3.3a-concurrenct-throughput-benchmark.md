# Story 3.3a: Implement Foundational Concurrent Throughput Benchmark

## Status: Completed

## Completion Notes:
Implemented foundational concurrent throughput benchmark including two-stage intelligent search for parallel and batch-size, binary search for VRAM limit detection, and updated CLI and output generator to support the new functionality. All unit and integration tests passed.

## Story

-   As a Researcher (Randy),
-   I want the tool to benchmark `llama-server` by simulating concurrent requests and identifying the single best-performing configuration,
-   so that I can quickly find the optimal throughput settings for my hardware without manual trial and error.

## Acceptance Criteria (ACs)

1.  The benchmark is triggered by the `--benchmark` flag and only runs when a server-oriented use case is selected.
2.  A new `BenchmarkingEngine` method orchestrates the test, managing the `llama-server` subprocess lifecycle (start, health check, stop).
3.  The engine uses an `asyncio`-based client pool to simulate a **saturating concurrent load**, defined as spawning client tasks at least **2x the size of the `--parallel` value** being tested.
4.  The benchmark implements a **two-stage intelligent search**: first finding the optimal `--parallel` level, then tuning `--batch-size`.
5.  The benchmark uses a **binary search** to precisely identify the maximum `--n-gpu-layers` the VRAM can support, reporting the specific point of OOM failure.
6.  The final output reports the single configuration that achieved the highest tokens/second throughput.
7.  A new flag, `--max-vram-gb`, allows users to specify a VRAM budget, which the benchmark will respect.
8.  The new flag is documented in the `--help` output with a clear explanation.

## Tasks / Subtasks

-   [x] **Task 1: Implement `BenchmarkingEngine` Core Logic**:
    -   [x] Subtask 1.1: Create the new `benchmark_throughput` method.
    -   [x] Subtask 1.2: Implement robust `llama-server` subprocess management.
    -   [x] Subtask 1.3: Implement the two-stage search logic for `parallel` and `batch-size`.
    -   [x] Subtask 1.4: Implement the binary search logic for VRAM limit detection.
-   [x] **Task 2: Build Concurrent Client Simulator**:
    -   [x] Subtask 2.1: Implement the `asyncio` and `aiohttp` client pool.
    -   [x] Subtask 2.2: Implement the logic for creating a saturating load.
-   [x] **Task 3: Update `CLI` and `Output Generator`**:
    -   [x] Subtask 3.1: Add the `--max-vram-gb` flag and pass it to the engine.
    -   [x] Subtask 3.2: Update the output components to report the single best configuration and the detected VRAM cliff.

## Dev Notes

The primary goal of this story is to build the core machinery for concurrent testing. It must reliably start a server, hit it with a realistic load, and find the best-performing configuration. Focus on the robustness of the process management and the intelligent search to minimize benchmark duration.

### Testing

-   [x] **Unit Tests**: Cover the binary search logic and the two-stage search parameter generation.
-   [x] **Integration Test**: Verify the `BenchmarkingEngine` can correctly start, check the health of, and stop a mock `llama-server` subprocess.
-   [x] **E2E Test**: Run the benchmark with a mock server and assert that it correctly identifies a predefined "best" configuration from a mock dataset.

