# Story 1.5: Perform Pre-flight Feasibility Check

## Status: Approved

## Story

- As a <PERSON><PERSON> (<PERSON><PERSON>-and-Play <PERSON>)
- I want the tool to perform a quick feasibility check before running a full benchmark
- so that I'm immediately warned if the selected model is too large for my system's RAM or VRAM.

## Acceptance Criteria (ACs)

1. The tool estimates the model's required memory footprint based on its size and quantization type.
2. The tool compares this estimated footprint against the detected system RAM and VRAM.
3. If the model is unlikely to fit in system RAM, the tool must exit with a clear warning, advising the user to try a smaller or more quantized model.
4. The feasibility check must account for a reasonable amount of system overhead (e.g., 2-4 GB of RAM) to prevent Out-of-Memory (OOM) errors.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In the `analyzer` component, implement a function `estimate_memory_footprint(model: ModelProfile)` that returns the estimated RAM and VRAM requirements in GB.
- [ ] Task 2 (AC: 2, 3, 4): Implement a function `run_feasibility_check(model: ModelProfile, system: SystemProfile)`.
  - [ ] Subtask 2.1: This function will call `estimate_memory_footprint`.
  - [ ] Subtask 2.2: It will compare the estimated requirements against the system's available resources, leaving a safety margin for OS overhead.
  - [ ] Subtask 2.3: If the check fails, the function should raise a specific `FeasibilityError` with a clear, user-friendly message.
- [ ] Task 3 (AC: 1, 2, 3): Write unit tests for both the estimation logic and the pass/fail scenarios of the feasibility check.
- [ ] Task 4 (AC: 3): The main `CLI` component must call `run_feasibility_check` immediately after gathering the system and model profiles, and before starting the wizard or benchmark. It must catch the `FeasibilityError` and print its message before exiting.

## Dev Notes

The goal of this check is to fail fast and prevent user frustration. The estimation logic does not need to be perfectly precise, but it should be conservative to reliably prevent OOM errors during the actual benchmark.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/analyzer/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Create an E2E test that uses a mock `SystemProfile` with low RAM and asserts that the tool exits with the correct error message when given a large model profile.

**Manual Test Steps**: Attempt to run `llama-tune` with a known very large model on a machine with low RAM/VRAM and confirm the tool exits early with a clear warning message.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |