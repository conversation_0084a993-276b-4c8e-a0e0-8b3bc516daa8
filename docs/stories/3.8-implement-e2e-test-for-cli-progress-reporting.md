# Story 3.8: Implement E2E Test for CLI Progress Reporting

## Status: Draft

## Story

-   **As a** Product Owner,
-   **I want** a dedicated end-to-end test for the real-time progress display in the CLI,
-   **so that** I can be confident that users are receiving clear and accurate feedback during long-running benchmark operations and prevent future regressions.

## Acceptance Criteria (ACs)

1.  A new test file will be added to the `tests/e2e/` directory.
2.  The test will execute the `benchmark` command with a minimal set of parameters to trigger the progress display.
3.  The test must capture the `stdout` and/or `stderr` streams of the CLI process.
4.  The captured output must be asserted to contain expected strings and patterns that confirm the progress bar was rendered (e.g., presence of `[%]`, phase descriptions like "Phase 1/2", and run counters like "Run 1/3"). The test should leverage `click.testing.CliRunner` or `pytest-console-scripts` for robust CLI output capture and assertion.
5.  The test must successfully run as part of the existing E2E test suite.

## Tasks / Subtasks

-   [ ] **Task 1 (AC: 1, 2):** Create a new E2E test file and a test case that runs the `benchmark` command.
-   [ ] **Task 2 (AC: 3, 4):** Implement logic to capture the command's output and assert that the progress reporting elements are present.
-   [ ] **Task 3 (AC: 5):** Ensure the new test is discovered and executed correctly by the existing test runner.

## Dependencies

-   This story is dependent on Story 3.6: "Implement Statistically Significant Benchmark Measurements" being completed, as the progress display will only include run counters (`Run 1/3`) once multi-run functionality is implemented.
