# Story 1.7: Recommend Memory Lock for Stable Performance

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want the tool to recommend using `--mlock` when appropriate
- so that the model is locked into RAM, preventing it from being swapped to disk and ensuring consistent performance.

## Acceptance Criteria (ACs)

1. The tool compares the model's file size against the available system RAM.
2. If the model can comfortably fit into RAM with a safety margin, the `--mlock` argument is added to the generated command.
3. If the model is too large to safely lock into RAM, the `--mlock` argument is omitted.
4. The rationale for including or omitting `--mlock` is explained in the `--verbose` output.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In the `analyzer` component, implement a function `should_recommend_mlock(model: ModelProfile, system: SystemProfile)` that returns a boolean.
- [ ] Task 2 (AC: 1): The function logic should compare the model's file size to `system.total_ram_gb`.
- [ ] Task 3 (AC: 2): Implement a conservative safety margin (e.g., recommend `mlock` only if model size is < 80% of total RAM).
- [ ] Task 4 (AC: 2, 3): The `Output Generator` component must call `should_recommend_mlock` and add the `--mlock` flag to the final command only if it returns `True`.
- [ ] Task 5 (AC: 4): The `Output Generator` must add a note to the verbose output explaining the decision.
- [ ] Task 6 (AC: 1, 2, 3): Write unit tests for the `should_recommend_mlock` function, covering cases where it should return `True` and `False`.

## Dev Notes

The logic for this recommendation must be conservative. It is better to wrongly omit the `--mlock` flag than to recommend it and cause system instability on a user's machine due to low available memory.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/analyzer/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Create two E2E tests using mocked `SystemProfile` and `ModelProfile` objects—one with enough RAM to trigger the recommendation, and one without. Assert the presence or absence of the `--mlock` flag in the final command.

**Manual Test Steps**: Run the tool with a model that is small relative to your system's RAM and check that `--mlock` is recommended in the verbose output and present in the final command. Then, test with a very large model and ensure the flag is omitted.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |