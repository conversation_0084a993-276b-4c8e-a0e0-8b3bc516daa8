# Story 3.5: Display Real-time Benchmark Progress

## Status: Approved

## Story

- As a Hobbyist (<PERSON><PERSON>)
- I want the tool to provide clear and continuous progress updates during the benchmark
- so that I know what it's testing and can estimate the time remaining.

## Acceptance Criteria (ACs)

1. The tool must display the current benchmark phase (e.g., "Phase 1/3: Testing GPU Layer Offload...").
2. For each phase, it must show the current step (e.g., "Testing --n-gpu-layers 25/33").
3. A progress bar or percentage completion indicator is displayed for the overall benchmark process.
4. Key performance results (e.g., tokens/sec) for each step are displayed as they are recorded.

## Tasks / Subtasks

- [ ] Task 1 (AC: 3): Integrate a library like `rich` or `tqdm` to handle rich terminal output and progress bars. Add it as a dependency using Poetry.
- [ ] Task 2 (AC: 1, 2): The `BenchmarkingEngine` must be refactored to report its progress back to the `CLI` component (e.g., via a callback function or a shared state object).
- [ ] Task 3 (AC: 3, 4): The `CLI` component will be responsible for initializing and updating the progress bar display with the information received from the `BenchmarkingEngine`.
- [ ] Task 4 (AC: 4): Ensure the real-time performance results (tokens/sec) are displayed alongside the progress bar update.
- [ ] Task 5 (AC: 1, 2, 3): Write unit tests for the progress reporting logic to ensure it formats the output correctly given a specific progress state.

## Dev Notes

This feature is critical for preventing the user from thinking the application has frozen during a long benchmark. The `rich` library is highly recommended as it provides excellent support for progress bars, tables, and other formatted output with minimal effort.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/cli/`), coverage requirement: 85%. Test the progress update formatting logic.
- [ ] Integration Test
- [ ] E2E Test

**Manual Test Steps**: Run `llama-tune --benchmark` and visually confirm that a progress bar appears and updates throughout the benchmark process, showing the current phase and step.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |