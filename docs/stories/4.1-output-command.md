# Story 4.1: Output Final Command for Execution

## Status: Approved

## Story

- As a Novi<PERSON> (<PERSON><PERSON>-and-<PERSON>)
- I want the tool's primary output to be the complete and valid `llama-server` command
- so that I can copy and paste it directly into my terminal to run the server.

## Acceptance Criteria (ACs)

1. By default, the tool's final output to standard-out is exclusively the `llama-server` command string.
2. The generated command includes all optimal parameters determined by the hardware analysis (Epic 1) and/or benchmark phases (Epic 3).
3. The command is syntactically correct and ready for immediate execution.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 2): In the `Output Generator` component (within `src/llama_tune/reporting/`), implement a method to format the final command string.
- [ ] Task 2 (AC: 2): This method will take the `OptimalConfiguration` data model as input and assemble the command string from its properties.
- [ ] Task 3 (AC: 1): The main `CLI` component will call this method by default and print the returned string to `stdout`. Nothing else should be printed to `stdout` in this mode.
- [ ] Task 4 (AC: 3): Write unit tests for the command formatting logic, ensuring correct assembly and syntax for various input `OptimalConfiguration` objects.

## Dev Notes

This is the default output mode. The function should be careful to only include parameters that have been determined. For example, if no GPU is found, `--n-gpu-layers` should not be present. The output should be *only* the command, with no extra text, to allow for easy redirection and piping in shell scripts.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/reporting/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Run a full benchmark with a mocked backend and assert that the final `stdout` is a correctly formatted command string and nothing else.

**Manual Test Steps**: Run `llama-tune --benchmark` and copy-paste the output command directly into the terminal to ensure it's valid and runs without syntax errors.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |