# Story 6.1: Recommend API Key for Server Security

## Status: Approved

## Story

- As an Advanced User (Researcher Randy) deploying a public-facing server
- I want the tool to automatically include a recommendation for an API key in the generated command
- so that my server endpoint is secured by default.

## Acceptance Criteria (ACs)

1. When generating a command for a server use case (e.g., via the wizard), the tool adds the `--api-key` parameter to the command.
2. The command should include a placeholder value for the key, clearly indicating the user must replace it (e.g., `--api-key 'YOUR_SECRET_KEY_HERE'`).
3. The `--verbose` output includes a security note explaining the importance of setting a strong, unique API key for any public-facing server.
4. This feature is only active when generating a server command, not for simple one-off inference runs.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1, 2): In the `Output Generator` component, modify the command generation logic.
- [ ] Task 2 (AC: 1, 4): Add a check to see if the configuration is for a server use case.
- [ ] Task 3 (AC: 2): If it is a server use case, append the `--api-key` flag to the command string with a placeholder value like `'YOUR_SECRET_KEY_HERE'`.
- [ ] Task 4 (AC: 3): In the verbose output generation method, add a security note explaining the importance of the API key.
- [ ] Task 5 (AC: 1, 4): Write unit tests to verify that the `--api-key` flag is correctly added for server-type configurations and omitted otherwise.

## Dev Notes

This is a simple but critical feature for responsible tool development. We are not generating a key, merely reminding the user to set one by adding the flag and a placeholder to the command. This helps prevent users from accidentally deploying an insecure server.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/reporting/`), coverage requirement: 85%
- [ ] Integration Test
- [x] E2E Test: Run the wizard, select a server use-case, and assert that the final command includes the `--api-key` flag with the placeholder. Run again with a non-server use case and assert the flag is absent.

**Manual Test Steps**: Run the interactive wizard and select a server preset. Verify the final command contains `--api-key 'YOUR_SECRET_KEY_HERE'`.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |