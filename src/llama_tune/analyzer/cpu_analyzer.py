import platform
import subprocess
import re
import logging

logger = logging.getLogger(__name__)

def get_physical_cpu_cores() -> int:
    """
    Detects the number of physical CPU cores on the system.

    Returns:
        int: The number of physical CPU cores, or 0 if detection fails.
    """
    system = platform.system()

    if system == "Linux":
        return _get_physical_cpu_cores_linux()
    elif system == "Windows":
        return _get_physical_cpu_cores_windows()
    elif system == "Darwin":  # macOS
        return _get_physical_cpu_cores_macos()
    else:
        logger.warning(f"Unsupported operating system: {system}. Cannot detect physical CPU cores.")
        return 0

def _get_physical_cpu_cores_linux() -> int:
    """
    Detects physical CPU cores on Linux using lscpu.
    """
    try:
        result = subprocess.run(['lscpu'], capture_output=True, text=True, check=True)
        output = result.stdout
        
        # Extract "Core(s) per socket" and "Socket(s)"
        cores_per_socket_match = re.search(r'^Core\(s\) per socket:\s*(\d+)', output, re.MULTILINE)
        sockets_match = re.search(r'^Socket\(s\):\s*(\d+)', output, re.MULTILINE)

        if cores_per_socket_match and sockets_match:
            cores_per_socket = int(cores_per_socket_match.group(1))
            sockets = int(sockets_match.group(1))
            return cores_per_socket * sockets
        else:
            logger.warning("Could not parse 'lscpu' output for physical core count on Linux.")
            return 0
    except FileNotFoundError:
        logger.warning("'lscpu' command not found. Cannot detect physical CPU cores on Linux.")
        return 0
    except subprocess.CalledProcessError as e:
        logger.warning(f"Error running 'lscpu' on Linux: {e}. Stderr: {e.stderr}")
        return 0
    except Exception as e:
        logger.warning(f"An unexpected error occurred during Linux CPU detection: {e}")
        return 0

def _get_physical_cpu_cores_windows() -> int:
    """
    Detects physical CPU cores on Windows using wmic.
    """
    try:
        # Use WMIC to get the number of Cores (not LogicalProcessors)
        result = subprocess.run(
            ['wmic', 'cpu', 'get', 'NumberOfCores', '/value'],
            capture_output=True, text=True, check=True, shell=True
        )
        output = result.stdout
        
        total_cores = 0
        for line in output.splitlines():
            if line.startswith("NumberOfCores="):
                try:
                    total_cores += int(line.split('=')[1])
                except ValueError:
                    logger.warning(f"Could not parse NumberOfCores value: {line}")
        
        if total_cores > 0:
            return total_cores
        else:
            logger.warning("Could not determine physical core count from WMIC on Windows.")
            return 0
    except FileNotFoundError:
        logger.warning("'wmic' command not found. Cannot detect physical CPU cores on Windows.")
        return 0
    except subprocess.CalledProcessError as e:
        logger.warning(f"Error running 'wmic' on Windows: {e}. Stderr: {e.stderr}")
        return 0
    except Exception as e:
        logger.warning(f"An unexpected error occurred during Windows CPU detection: {e}")
        return 0

def _get_physical_cpu_cores_macos() -> int:
    """
    Detects physical CPU cores on macOS using sysctl.
    """
    try:
        # Get number of physical cores
        result = subprocess.run(
            ['sysctl', '-n', 'hw.physicalcpu'],
            capture_output=True, text=True, check=True
        )
        physical_cores = int(result.stdout.strip())
        return physical_cores
    except FileNotFoundError:
        logger.warning("'sysctl' command not found. Cannot detect physical CPU cores on macOS.")
        return 0
    except subprocess.CalledProcessError as e:
        logger.warning(f"Error running 'sysctl' on macOS: {e}. Stderr: {e.stderr}")
        return 0
    except ValueError:
        logger.warning("Could not parse 'sysctl hw.physicalcpu' output for physical core count on macOS.")
        return 0
    except Exception as e:
        logger.warning(f"An unexpected error occurred during macOS CPU detection: {e}")
        return 0