from typing import Dict, Any

SAMPLING_PRESETS: Dict[str, Dict[str, Any]] = {
    "Conversational Chatbot": {
        "temperature": 0.7,
        "top_p": 0.9,
        "repeat_penalty": 1.1,
        "presence_penalty": 0.0,
        "frequency_penalty": 0.0,
    },
    "RAG / Document Analysis": {
        "temperature": 0.1,
        "top_p": 0.95,
        "repeat_penalty": 1.0,
        "presence_penalty": 0.0,
        "frequency_penalty": 0.0,
    },
    "Code Completion": {
        "temperature": 0.2,
        "top_p": 0.9,
        "repeat_penalty": 1.0,
        "presence_penalty": 0.0,
        "frequency_penalty": 0.0,
    },
    "Custom / Max Performance": {}
}