import json
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any

@dataclass
class GpuInfo:
    model_name: str
    vram_gb: float

@dataclass
class SystemProfile:
    cpu_cores: int
    total_ram_gb: float
    gpus: List[GpuInfo]
    numa_detected: bool
    blas_backend: str

@dataclass
class ModelProfile:
    file_path: str
    architecture: str
    layer_count: int
    quantization_type: str

@dataclass
class BenchmarkResult:
    n_gpu_layers: int
    prompt_speed_tps: float
    generation_speed_tps: float
    batch_size: Optional[int]
    parallel_level: Optional[int]
    # New fields for statistical analysis
    prompt_speed_tps_mean: float = 0.0
    prompt_speed_tps_std: float = 0.0
    generation_speed_tps_mean: float = 0.0
    generation_speed_tps_std: float = 0.0
    # Store individual run results for statistical analysis
    individual_results: Optional[List[Dict[str, float]]] = None
    notes: Optional[List[str]] = None

@dataclass
class OptimalConfiguration:
    system_profile: SystemProfile
    model_profile: ModelProfile
    best_benchmark_result: BenchmarkResult
    generated_command: str  # Will be deprecated - OutputGenerator should build this
    notes: List[str]
    # User configuration choices
    ctx_size: Optional[int] = None
    sampling_parameters: Optional[Dict[str, Any]] = None
    use_case: Optional[str] = None # Added for Story 3.3 to determine benchmark phase
    max_vram_gb: Optional[float] = None

    def to_dict(self):
        """Converts the OptimalConfiguration object to a dictionary."""
        return asdict(self)