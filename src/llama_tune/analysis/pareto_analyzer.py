from typing import List, Dict, Any

def is_dominated(p: Dict[str, Any], q: Dict[str, Any], objectives: List[str]) -> bool:
    """Checks if point p is dominated by point q.

    For performance objectives (gen_speed, prompt_speed), higher is better.
    For resource objectives (layers), lower is better.
    """
    # Define which objectives should be maximized vs minimized
    maximize_objectives = {"gen_speed", "prompt_speed", "generation_speed_tps", "prompt_speed_tps"}

    at_least_as_good = True
    strictly_better_in_one = False

    for obj in objectives:
        if obj in maximize_objectives:
            # For maximization: q dominates p if q[obj] >= p[obj]
            if q[obj] < p[obj]:
                at_least_as_good = False
                break
            if q[obj] > p[obj]:
                strictly_better_in_one = True
        else:
            # For minimization: q dominates p if q[obj] <= p[obj]
            if q[obj] > p[obj]:
                at_least_as_good = False
                break
            if q[obj] < p[obj]:
                strictly_better_in_one = True

    return at_least_as_good and strictly_better_in_one

def get_pareto_front(points: List[Dict[str, Any]], objectives: List[str]) -> List[Dict[str, Any]]:
    """Calculates the Pareto front for a given set of points and objectives."""
    pareto_front = []
    for p in points:
        is_p_dominated = False
        for q in points:
            if p == q:
                continue
            if is_dominated(p, q, objectives):
                is_p_dominated = True
                break
        if not is_p_dominated:
            pareto_front.append(p)
    return pareto_front
