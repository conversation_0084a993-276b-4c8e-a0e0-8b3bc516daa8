import logging
import subprocess
import re
import asyncio
import httpx
import time
from typing import List, Optional, Dict, Any, IO, Callable, Tuple
import statistics
from llama_tune.core.data_models import SystemProfile, ModelProfile, OptimalConfiguration, BenchmarkResult
from llama_tune.caching.cache_manager import CacheManager

# Define a type alias for the progress callback function
ProgressCallback = Callable[[str, str, int, int, Optional[float], Optional[int], Optional[int]], None]

from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile

logger = logging.getLogger(__name__)

class BenchmarkingEngine:
    """
    Orchestrates the automated benchmarking process.
    """
    def __init__(self, llama_server_url: Optional[str] = None, use_cache: bool = True):
        self.llama_server_url = llama_server_url
        self.use_cache = use_cache
        self.cache_manager = CacheManager() if use_cache else None

    def run_benchmark(self, model_path: str, ctx_size: int, use_case: str, initial_num_runs: int, progress_callback: Optional[ProgressCallback] = None, max_vram_gb: Optional[float] = None) -> Tuple[OptimalConfiguration, List[BenchmarkResult]]:
        """
        Executes the full hardware and model analysis as a prerequisite
        before starting the benchmark.
        """
        logger.info("BenchmarkingEngine: Initiating benchmark process.")

        system_profile = get_system_profile()
        logger.info(f"BenchmarkingEngine: Detected System Profile: {system_profile}")

        model_profile = get_model_profile(model_path)
        logger.info(f"BenchmarkingEngine: Detected Model Profile: {model_profile}")

        # Check cache first if caching is enabled
        if self.use_cache and self.cache_manager:
            cache_key = self.cache_manager.generate_cache_key(
                system_profile, model_profile, ctx_size, use_case, max_vram_gb
            )
            cached_config = self.cache_manager.get_from_cache(cache_key)
            if cached_config:
                logger.info("Found cached benchmark result, returning immediately.")
                # Return cached result with empty benchmark results list since we didn't run new benchmarks
                return cached_config, []

        logger.info(f"Benchmarking for model: {model_profile.file_path} with context size: {ctx_size}")
        
        # Total phases for the overall progress bar
        total_phases = 1
        if use_case == "multi-user-server":
            total_phases = 2

        # Phase 1: GPU Offload Benchmark
        if progress_callback:
            progress_callback("Phase 1/{} GPU Offload".format(total_phases), "Starting...", 0, 1, None, None, None)
        best_benchmark_result, all_gpu_offload_results = self._run_gpu_offload_benchmark(model_profile, ctx_size, initial_num_runs, progress_callback, max_vram_gb)

        optimal_config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=best_benchmark_result,
            generated_command="", # This will be generated by OutputGenerator
            notes=["GPU offload benchmark completed."],
            ctx_size=ctx_size,
            sampling_parameters={},
            use_case=use_case,
            max_vram_gb=max_vram_gb
        )

        # Transfer high variance warnings from best_benchmark_result to optimal_config notes
        if best_benchmark_result.notes:
            optimal_config.notes.extend(best_benchmark_result.notes)

        # Phase 2: Throughput Benchmark (Batching and Concurrency)
        if use_case == "multi-user-server":
            logger.info("BenchmarkingEngine: User goal is multi-user server deployment. Starting throughput benchmark (batching and concurrency).")
            optimal_config.notes.append("Throughput benchmark (batching and concurrency) initiated.")
            if progress_callback:
                progress_callback("Phase 2/{} Throughput".format(total_phases), "Starting...", 0, 1, None, None, None)
            best_throughput_result = self._run_throughput_benchmark(
                model_profile,
                ctx_size,
                optimal_config.best_benchmark_result.n_gpu_layers, # Use optimal n_gpu_layers from previous phase
                initial_num_runs, # Pass initial_num_runs to throughput benchmark
                progress_callback
            )
            # The throughput benchmark provides the optimal batch_size and parallel_level
            # for a multi-user scenario. These results should be integrated, not compared,
            # as they represent a different dimension of performance (server throughput vs. single-stream).
            
            # Integrate throughput results into the optimal configuration.
            optimal_config.best_benchmark_result.batch_size = best_throughput_result.batch_size
            optimal_config.best_benchmark_result.parallel_level = best_throughput_result.parallel_level
            
            # The generation_speed_tps from the throughput benchmark represents overall server throughput,
            # which is the more relevant metric for this use case. We update it, but keep the
            # original prompt_speed_tps from the single-stream test for informational purposes.
            optimal_config.best_benchmark_result.generation_speed_tps = best_throughput_result.generation_speed_tps
            optimal_config.best_benchmark_result.generation_speed_tps_mean = best_throughput_result.generation_speed_tps_mean
            optimal_config.best_benchmark_result.generation_speed_tps_std = best_throughput_result.generation_speed_tps_std

            optimal_config.notes.append(f"Optimal throughput configuration found: batch_size={best_throughput_result.batch_size}, parallel_level={best_throughput_result.parallel_level}.")
            optimal_config.notes.append(f"Achieved server throughput: {best_throughput_result.generation_speed_tps:.2f} t/s.")

            # Transfer high variance warnings from best_throughput_result to optimal_config notes
            if best_throughput_result.notes:
                optimal_config.notes.extend(best_throughput_result.notes)

        # Save to cache if caching is enabled
        if self.use_cache and self.cache_manager:
            cache_key = self.cache_manager.generate_cache_key(
                system_profile, model_profile, ctx_size, use_case, max_vram_gb
            )
            self.cache_manager.save_to_cache(cache_key, optimal_config)

        return optimal_config, all_gpu_offload_results

    def _check_n_gpu_layers(self, model_profile: ModelProfile, ctx_size: int, n_gpu_layers: int) -> Optional[BenchmarkResult]:
        """
        Helper method to run llama-bench for a given n_gpu_layers and return BenchmarkResult if successful.
        Returns None if OOM or other error occurs.
        """
        command = [
            "llama-bench",
            "-m", model_profile.file_path,
            "-p", f"\"--n-gpu-layers {n_gpu_layers} --ctx-size {ctx_size}\"",
            "-n", "128", # Number of tokens to generate
            "-c", str(ctx_size) # Context size
        ]
        
        try:
            process = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True,
                encoding='utf-8'
            )
            stdout = process.stdout
            stderr = process.stderr

            if stderr:
                logger.warning(f"llama-bench stderr for n_gpu_layers={n_gpu_layers}:\n{stderr}")

            pp_speed_match = re.search(r"prompt processing speed:\s*([\d.]+)\s*t/s", stdout)
            tg_speed_match = re.search(r"token generation speed:\s*([\d.]+)\s*t/s", stdout)

            pp_speed = float(pp_speed_match.group(1)) if pp_speed_match else 0.0
            tg_speed = float(tg_speed_match.group(1)) if tg_speed_match else 0.0

            current_result = BenchmarkResult(
                n_gpu_layers=n_gpu_layers,
                prompt_speed_tps=pp_speed,
                generation_speed_tps=tg_speed,
                batch_size=None,
                parallel_level=None
            )
            return current_result

        except subprocess.CalledProcessError as e:
            logger.error(f"llama-bench crashed for --n-gpu-layers {n_gpu_layers}. This likely indicates a VRAM limit.")
            logger.error(f"Command: {' '.join(e.cmd)}")
            logger.error(f"Return Code: {e.returncode}")
            logger.error(f"Stdout:\n{e.stdout}")
            logger.error(f"Stderr:\n{e.stderr}")
            return None
        except FileNotFoundError:
            logger.error("llama-bench executable not found. Please ensure it's in your PATH.")
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred during llama-bench execution: {e}")
            return None

    def _run_gpu_offload_benchmark(self, model_profile: ModelProfile, ctx_size: int, initial_num_runs: int, progress_callback: Optional[ProgressCallback] = None, max_vram_gb: Optional[float] = None) -> Tuple[BenchmarkResult, List[BenchmarkResult]]:
        """
        Task 1 (AC: 1, 2, 3): Modify the _run_gpu_offload_benchmark method to include the stability check logic.
        """
        logger.info("Starting GPU offload benchmark with binary search and statistical stability check...")
        
        best_result: Optional[BenchmarkResult] = None
        all_successful_results: List[BenchmarkResult] = []
        
        low = 0
        high = model_profile.layer_count
        
        # Store the highest known good configuration
        highest_successful_layers = -1
        
        # Approximate steps for progress bar (binary search takes log2(N) steps)
        # Max 10 runs per n_gpu_layers check
        total_steps = (high.bit_length() + 1) * 10 if high > 0 else 10 # +1 for safety, ensure at least 1 step
        current_step_count = 0

        while low <= high:
            n_gpu_layers = (low + high) // 2
            
            # Perform multiple runs for statistical significance and stability check
            individual_run_results: List[Dict[str, float]] = []
            all_generation_speeds_for_current_n_gpu_layers: List[float] = []
            
            current_run_count = 0
            is_stable = False
            high_variance_warning = False

            while current_run_count < 10: # Max 10 runs
                current_run_count += 1
                current_step_count += 1
                
                status_message = "Running"
                if current_run_count >= 3:
                    if is_stable:
                        status_message = "Stable"
                    elif current_run_count == 10:
                        status_message = "Unstable (Max Runs)"
                        high_variance_warning = True
                    else:
                        status_message = "Unstable"

                step_description = f"Testing --n-gpu-layers {n_gpu_layers} (Run {current_run_count}/10) ({status_message})"
                logger.info(f"Benchmarking with --n-gpu-layers {n_gpu_layers} (Run {current_run_count}/10)")

                if progress_callback:
                    progress_callback("Phase 1/2 GPU Offload", step_description, current_step_count, total_steps, None, current_run_count, 10)

                # VRAM budget check (optional)
                if max_vram_gb is not None:
                    # This is a simplified estimation. A more accurate one would require model-specific data.
                    estimated_vram_usage = (n_gpu_layers / model_profile.layer_count) * model_profile.layer_count * 0.1 # Placeholder heuristic, adjust as needed
                    if estimated_vram_usage > max_vram_gb:
                        logger.info(f"Skipping --n-gpu-layers {n_gpu_layers} due to estimated VRAM usage ({estimated_vram_usage:.2f} GB) exceeding budget ({max_vram_gb:.2f} GB).")
                        current_result = None # Treat as failure for binary search
                        break # Break from inner run loop

                current_result = self._check_n_gpu_layers(model_profile, ctx_size, n_gpu_layers)

                if current_result is not None:
                    individual_run_results.append({
                        "prompt_speed_tps": current_result.prompt_speed_tps,
                        "generation_speed_tps": current_result.generation_speed_tps
                    })
                    all_generation_speeds_for_current_n_gpu_layers.append(current_result.generation_speed_tps)

                    if progress_callback:
                        progress_callback("Phase 1/2 GPU Offload", step_description, current_step_count, total_steps, current_result.generation_speed_tps, current_run_count, 10)
                    
                    # Check for stability after a minimum of 3 runs
                    if current_run_count >= 3:
                        mean_gen_speed = statistics.mean(all_generation_speeds_for_current_n_gpu_layers)
                        # Use pvariance for population variance if we assume these runs are the entire population,
                        # or variance for sample variance if these are a sample.
                        # Given the context of "stability check", sample variance is more appropriate.
                        if len(all_generation_speeds_for_current_n_gpu_layers) > 1:
                            std_dev_gen_speed = statistics.stdev(all_generation_speeds_for_current_n_gpu_layers)
                        else:
                            std_dev_gen_speed = 0.0 # Cannot calculate std dev with one sample

                        if mean_gen_speed > 0 and (std_dev_gen_speed / mean_gen_speed) <= 0.05:
                            is_stable = True
                            logger.info(f"Results stable for --n-gpu-layers {n_gpu_layers} after {current_run_count} runs (StdDev/Mean: {std_dev_gen_speed/mean_gen_speed:.4f})")
                            break # Stability achieved, break inner loop
                        else:
                            logger.info(f"Results unstable for --n-gpu-layers {n_gpu_layers} after {current_run_count} runs (StdDev/Mean: {std_dev_gen_speed/mean_gen_speed:.4f}). Continuing...")
                else:
                    logger.warning(f"llama-bench failed for --n-gpu-layers {n_gpu_layers} (Run {current_run_count}/10). Treating as VRAM limit reached.")
                    break # Break from inner loop if any run fails

            # After inner while loop (either stable or max runs reached)
            if current_result is not None and len(all_generation_speeds_for_current_n_gpu_layers) > 0:
                mean_prompt_speed = statistics.mean([r["prompt_speed_tps"] for r in individual_run_results])
                mean_generation_speed = statistics.mean(all_generation_speeds_for_current_n_gpu_layers)
                
                prompt_speed_std = statistics.stdev([r["prompt_speed_tps"] for r in individual_run_results]) if len(individual_run_results) > 1 else 0.0
                generation_speed_std = statistics.stdev(all_generation_speeds_for_current_n_gpu_layers) if len(all_generation_speeds_for_current_n_gpu_layers) > 1 else 0.0

                notes = []
                if high_variance_warning:
                    notes.append("High Variance Warning: Results did not stabilize after 10 runs.")

                current_benchmark_result = BenchmarkResult(
                    n_gpu_layers=n_gpu_layers,
                    prompt_speed_tps=mean_prompt_speed,
                    generation_speed_tps=mean_generation_speed,
                    batch_size=None,
                    parallel_level=None,
                    prompt_speed_tps_mean=mean_prompt_speed,
                    prompt_speed_tps_std=prompt_speed_std,
                    generation_speed_tps_mean=mean_generation_speed,
                    generation_speed_tps_std=generation_speed_std,
                    individual_results=individual_run_results,
                    notes=notes if notes else None
                )

                # Update best_result if this one is better, or if performance is equal and n_gpu_layers is higher
                if best_result is None or \
                   self._calculate_performance_score(current_benchmark_result.prompt_speed_tps, current_benchmark_result.generation_speed_tps) > self._calculate_performance_score(best_result.prompt_speed_tps, best_result.generation_speed_tps) or \
                   (self._calculate_performance_score(current_benchmark_result.prompt_speed_tps, current_benchmark_result.generation_speed_tps) == self._calculate_performance_score(best_result.prompt_speed_tps, best_result.generation_speed_tps) and current_benchmark_result.n_gpu_layers > best_result.n_gpu_layers):
                    best_result = current_benchmark_result
                all_successful_results.append(current_benchmark_result)
                
                # Try for more layers
                low = n_gpu_layers + 1
            else:
                logger.warning(f"Failed to run all benchmarks for --n-gpu-layers {n_gpu_layers}. Trying fewer layers.")
                
                if progress_callback:
                    progress_callback("Phase 1/2 GPU Offload", step_description, current_step_count, total_steps, None, None, None)

                # This configuration failed, try with fewer layers
                high = n_gpu_layers - 1

        if not best_result:
            logger.warning("No successful benchmark runs for GPU offload.")
            fallback_result = BenchmarkResult(
                n_gpu_layers=0,
                prompt_speed_tps=0.0,
                generation_speed_tps=0.0,
                batch_size=None,
                parallel_level=None
            )
            return fallback_result, []

        # After the binary search, the best performing result is in `best_result`.
        # The `highest_successful_layers` gives us the VRAM cliff.
        logger.info(f"Binary search complete. VRAM limit appears to be just above {highest_successful_layers} layers.")
        logger.info(f"Optimal GPU offload configuration found: {best_result.n_gpu_layers} layers with composite score={self._calculate_performance_score(best_result.prompt_speed_tps, best_result.generation_speed_tps):.2f}")
        
        return best_result, all_successful_results

    async def _send_completion_request(self, client: httpx.AsyncClient, prompt: str, max_tokens: int, temperature: float = 0.7) -> Dict[str, Any]:
        """Sends a completion request to the llama-server."""
        server_url = self.llama_server_url if self.llama_server_url else "http://localhost:8080"
        try:
            response = await client.post(
                f"{server_url}/completion",
                json={
                    "prompt": prompt,
                    "n_predict": max_tokens,
                    "temperature": temperature,
                    "stream": False
                },
                timeout=60.0 # Increased timeout for potentially long generations
            )
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            logger.error(f"HTTP request failed: {e}")
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"An unexpected error occurred during HTTP request: {e}")
            return {"error": str(e)}

    async def _simulate_concurrent_clients(self, num_clients: int, prompt: str, max_tokens: int) -> float:
        """
        Simulates multiple concurrent clients sending requests to llama-server.
        Returns total tokens/second.
        """
        logger.info(f"Simulating {num_clients} concurrent clients...")
        start_time = time.time()
        
        async with httpx.AsyncClient() as client:
            tasks = [
                self._send_completion_request(client, prompt, max_tokens)
                for _ in range(num_clients)
            ]
            responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_generated_tokens = 0
        successful_requests = 0
        for res in responses:
            if isinstance(res, dict) and "content" in res:
                total_generated_tokens += len(res["content"].split()) # Simple token count by splitting words
                successful_requests += 1
            else:
                logger.error(f"Client request failed or returned unexpected format: {res}")

        end_time = time.time()
        duration = end_time - start_time

        if successful_requests == 0 or duration == 0:
            logger.warning("No successful requests or zero duration in client simulation.")
            return 0.0
        
        total_throughput = total_generated_tokens / duration
        logger.info(f"Client simulation completed. Total generated tokens: {total_generated_tokens}, Duration: {duration:.2f}s, Throughput: {total_throughput:.2f} t/s")
        return total_throughput

    def _calculate_performance_score(self, prompt_speed_tps: float, generation_speed_tps: float) -> float:
        """
        Calculate a composite performance score that considers both prompt processing and token generation speeds.
        This provides a more holistic view of performance as requested by the Product Owner.

        The scoring algorithm weights both metrics:
        - Token generation speed is weighted more heavily (70%) as it's typically the bottleneck for inference
        - Prompt processing speed is weighted less (30%) but still important for overall user experience

        Args:
            prompt_speed_tps: Prompt processing speed in tokens per second
            generation_speed_tps: Token generation speed in tokens per second

        Returns:
            Composite performance score (higher is better)
        """
        # Weights for the composite score
        GENERATION_WEIGHT = 0.7  # Token generation is typically the bottleneck
        PROMPT_WEIGHT = 0.3      # Prompt processing affects initial response time

        # Calculate weighted composite score
        composite_score = (generation_speed_tps * GENERATION_WEIGHT) + (prompt_speed_tps * PROMPT_WEIGHT)

        return composite_score

    def _analyze_concurrency_results(self, results: List[BenchmarkResult]) -> int:
        """
        Analyzes a list of BenchmarkResult objects from the concurrency testing phase
        to find the parallel_level that yielded the highest generation_speed_tps.
        """
        if not results:
            logger.warning("No concurrency benchmark results provided for analysis.")
            return 0

        best_parallel_level = results[0].parallel_level if results[0].parallel_level is not None else 0
        max_throughput = results[0].generation_speed_tps

        for result in results:
            if result.generation_speed_tps > max_throughput:
                max_throughput = result.generation_speed_tps
                best_parallel_level = result.parallel_level if result.parallel_level is not None else 0
        
        return best_parallel_level

    def _analyze_throughput_results(self, results: List[BenchmarkResult], n_gpu_layers: int) -> BenchmarkResult:
        """
        Analyzes a list of BenchmarkResult objects to find the one with the highest composite performance score.
        This considers both prompt processing speed and token generation speed for a holistic view.
        """
        if not results:
            logger.warning("No benchmark results provided for analysis.")
            return BenchmarkResult(
                n_gpu_layers=n_gpu_layers,
                prompt_speed_tps=0.0,
                generation_speed_tps=0.0,
                batch_size=None,
                parallel_level=None
            )

        best_result = results[0]
        best_score = self._calculate_performance_score(best_result.prompt_speed_tps, best_result.generation_speed_tps)

        for result in results:
            current_score = self._calculate_performance_score(result.prompt_speed_tps, result.generation_speed_tps)
            if current_score > best_score:
                best_score = current_score
                best_result = result
        return best_result

    def _run_throughput_benchmark(self, model_profile: ModelProfile, ctx_size: int, n_gpu_layers: int, initial_num_runs: int, progress_callback: Optional[ProgressCallback] = None) -> BenchmarkResult:
        """
        Task 1 (AC: 1, 2, 3): Modify the _run_throughput_benchmark method to include the stability check logic.
        """
        logger.info("Starting throughput benchmark (batching and concurrency) with statistical stability check...")

        benchmark_results: List[BenchmarkResult] = []

        # A simple prompt for testing
        test_prompt = "Tell me a short story about a brave knight and a dragon."
        max_tokens_to_generate = 128

        # Stage 1: Find optimal --parallel level
        logger.info("Stage 1: Finding optimal --parallel level.")
        parallel_results: List[BenchmarkResult] = []

        # Define a range of parallel levels to test
        parallel_levels_to_test = [1, 2, 4, 8, 16, 32] # Example values, can be made dynamic

        total_steps_stage1 = len(parallel_levels_to_test) * 10 # Max 10 runs per parallel level
        current_step_count_stage1 = 0

        for parallel_level in parallel_levels_to_test:
            individual_run_results_parallel: List[Dict[str, float]] = []
            all_throughput_speeds_for_current_parallel_level: List[float] = []
            
            current_run_count_parallel = 0
            is_stable_parallel = False
            high_variance_warning_parallel = False

            while current_run_count_parallel < 10: # Max 10 runs
                current_run_count_parallel += 1
                current_step_count_stage1 += 1
                
                status_message = "Running"
                if current_run_count_parallel >= 3:
                    if is_stable_parallel:
                        status_message = "Stable"
                    elif current_run_count_parallel == 10:
                        status_message = "Unstable (Max Runs)"
                        high_variance_warning_parallel = True
                    else:
                        status_message = "Unstable"

                step_description = f"Testing --parallel {parallel_level} (Run {current_run_count_parallel}/10) ({status_message})"
                if progress_callback:
                    progress_callback("Phase 2/2 Throughput (Stage 1)", step_description, current_step_count_stage1, total_steps_stage1, None, current_run_count_parallel, 10)

                if self.llama_server_url:
                    logger.info(f"Using existing llama-server at {self.llama_server_url}. Skipping server startup.")
                    server_startup_successful = True
                    server_process = None # No process to manage
                else:
                    # Start llama-server with a default batch size for this stage
                    server_command = [
                        "llama-server",
                        "-m", model_profile.file_path,
                        "--n-gpu-layers", str(n_gpu_layers),
                        "--ctx-size", str(ctx_size),
                        "--batch-size", "512", # Use a reasonably large batch size for parallel testing
                        "--threads", "4",
                        "--port", "8080"
                    ]

                    server_process: Optional[subprocess.Popen] = None
                    try:
                        logger.info(f"Starting llama-server: {' '.join(server_command)}")
                        server_process = subprocess.Popen(
                            server_command,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            encoding='utf-8'
                        )
                        time.sleep(5) # Give server time to start

                        server_startup_successful = False
                        if server_process.stdout:
                            for _ in range(10):
                                line = server_process.stdout.readline()
                                if "HTTP server listening" in line:
                                    server_startup_successful = True
                                    logger.info("llama-server started successfully.")
                                    break
                                if not line:
                                    break
                                logger.debug(f"Server startup output: {line.strip()}")

                        if not server_startup_successful:
                            logger.error("llama-server did not start successfully or timed out.")
                            if server_process.stderr:
                                server_stderr = server_process.stderr.read()
                                logger.error(f"llama-server stderr:\n{server_stderr}")
                            if server_process.poll() is not None:
                                logger.error(f"llama-server exited with code: {server_process.returncode}")
                            break # Break from inner run loop if server fails to start

                    except FileNotFoundError:
                        logger.error("llama-server executable not found. Please ensure it's in your PATH.")
                        break # Break from outer parallel loop
                    except Exception as e:
                        logger.error(f"An unexpected error occurred during llama-server or client execution: {e}")
                        if server_process and server_process.stderr:
                            server_stderr = server_process.stderr.read()
                            logger.error(f"llama-server stderr during error: {server_stderr}")
                        break # Break from inner run loop
                    finally:
                        if server_process and server_process.poll() is None:
                            logger.info("Terminating llama-server process.")
                            server_process.terminate()
                            server_process.wait(timeout=10)
                            if server_process.poll() is None:
                                logger.warning("llama-server did not terminate gracefully. Killing it.")
                                server_process.kill()
                    
                    if server_process and server_process.poll() is not None:
                        break # If server died, break outer loop too

                if not server_startup_successful:
                    break # Skip to next parallel level if server didn't start

                # Simulate saturating load: 2x the parallel level
                num_clients_to_spawn = parallel_level * 2
                try:
                    total_throughput = asyncio.run(self._simulate_concurrent_clients(num_clients_to_spawn, test_prompt, max_tokens_to_generate))
                    individual_run_results_parallel.append({"generation_speed_tps": total_throughput})
                    all_throughput_speeds_for_current_parallel_level.append(total_throughput)

                    if progress_callback:
                        progress_callback("Phase 2/2 Throughput (Stage 1)", step_description, current_step_count_stage1, total_steps_stage1, total_throughput, current_run_count_parallel, 10)

                    # Check for stability after a minimum of 3 runs
                    if current_run_count_parallel >= 3:
                        mean_throughput = statistics.mean(all_throughput_speeds_for_current_parallel_level)
                        if len(all_throughput_speeds_for_current_parallel_level) > 1:
                            std_dev_throughput = statistics.stdev(all_throughput_speeds_for_current_parallel_level)
                        else:
                            std_dev_throughput = 0.0

                        if mean_throughput > 0 and (std_dev_throughput / mean_throughput) <= 0.05:
                            is_stable_parallel = True
                            logger.info(f"Results stable for --parallel {parallel_level} after {current_run_count_parallel} runs (StdDev/Mean: {std_dev_throughput/mean_throughput:.4f})")
                            break # Stability achieved, break inner loop
                        else:
                            logger.info(f"Results unstable for --parallel {parallel_level} after {current_run_count_parallel} runs (StdDev/Mean: {std_dev_throughput/mean_throughput:.4f}). Continuing...")

                except Exception as e:
                    logger.error(f"Client simulation failed for parallel={parallel_level} (Run {current_run_count_parallel}/10): {e}")
                    break  # Break from inner run loop if client simulation fails

            # After inner while loop (either stable or max runs reached)
            if len(all_throughput_speeds_for_current_parallel_level) > 0:
                mean_throughput_parallel = statistics.mean(all_throughput_speeds_for_current_parallel_level)
                throughput_std_parallel = statistics.stdev(all_throughput_speeds_for_current_parallel_level) if len(all_throughput_speeds_for_current_parallel_level) > 1 else 0.0

                notes_parallel = []
                if high_variance_warning_parallel:
                    notes_parallel.append("High Variance Warning: Throughput results did not stabilize after 10 runs for this parallel level.")

                parallel_results.append(BenchmarkResult(
                    n_gpu_layers=n_gpu_layers,
                    prompt_speed_tps=0.0, # Not applicable for throughput benchmark
                    generation_speed_tps=mean_throughput_parallel,
                    batch_size=None,
                    parallel_level=parallel_level,
                    generation_speed_tps_mean=mean_throughput_parallel,
                    generation_speed_tps_std=throughput_std_parallel,
                    individual_results=individual_run_results_parallel,
                    notes=notes_parallel if notes_parallel else None
                ))
                logger.info(f"Result for parallel={parallel_level}: Mean Throughput={mean_throughput_parallel:.2f} t/s, StdDev={throughput_std_parallel:.2f}")
            else:
                logger.warning(f"No successful runs for --parallel {parallel_level}. Skipping to next parallel level.")

        best_parallel_level = self._analyze_concurrency_results(parallel_results)
        if best_parallel_level == 0:
            logger.error("Could not determine optimal parallel level. Exiting throughput benchmark.")
            return self._analyze_throughput_results(benchmark_results, n_gpu_layers)

        logger.info(f"Optimal parallel level found: {best_parallel_level}")

        # Stage 2: Tune --batch-size with the optimal --parallel level
        logger.info(f"Stage 2: Tuning --batch-size for optimal --parallel {best_parallel_level}.")
        
        batch_sizes_to_test = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512] # Example values, can be made dynamic

        total_steps_stage2 = len(batch_sizes_to_test) * 10 # Max 10 runs per batch size
        current_step_count_stage2 = 0

        for batch_size in batch_sizes_to_test:
            individual_run_results_batch: List[Dict[str, float]] = []
            all_throughput_speeds_for_current_batch_size: List[float] = []
            
            current_run_count_batch = 0
            is_stable_batch = False
            high_variance_warning_batch = False

            while current_run_count_batch < 10: # Max 10 runs
                current_run_count_batch += 1
                current_step_count_stage2 += 1
                
                status_message = "Running"
                if current_run_count_batch >= 3:
                    if is_stable_batch:
                        status_message = "Stable"
                    elif current_run_count_batch == 10:
                        status_message = "Unstable (Max Runs)"
                        high_variance_warning_batch = True
                    else:
                        status_message = "Unstable"

                step_description = f"Testing --batch-size {batch_size} (Run {current_run_count_batch}/10) ({status_message})"
                if progress_callback:
                    progress_callback("Phase 2/2 Throughput (Stage 2)", step_description, current_step_count_stage2, total_steps_stage2, None, current_run_count_batch, 10)

                if self.llama_server_url:
                    logger.info(f"Using existing llama-server at {self.llama_server_url}. Skipping server startup.")
                    server_startup_successful = True
                    server_process = None # No process to manage
                else:
                    server_command = [
                        "llama-server",
                        "-m", model_profile.file_path,
                        "--n-gpu-layers", str(n_gpu_layers),
                        "--ctx-size", str(ctx_size),
                        "--batch-size", str(batch_size),
                        "--threads", "4",
                        "--parallel", str(best_parallel_level), # Use the optimal parallel level
                        "--port", "8080"
                    ]

                    server_process: Optional[subprocess.Popen] = None
                    try:
                        logger.info(f"Starting llama-server: {' '.join(server_command)}")
                        server_process = subprocess.Popen(
                            server_command,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            encoding='utf-8'
                        )
                        time.sleep(5)

                        server_startup_successful = False
                        if server_process.stdout:
                            for _ in range(10):
                                line = server_process.stdout.readline()
                                if "HTTP server listening" in line:
                                    server_startup_successful = True
                                    logger.info("llama-server started successfully.")
                                    break
                                if not line:
                                    break
                                logger.debug(f"Server startup output: {line.strip()}")

                        if not server_startup_successful:
                            logger.error("llama-server did not start successfully or timed out.")
                            if server_process.stderr:
                                server_stderr = server_process.stderr.read()
                                logger.error(f"llama-server stderr:\n{server_stderr}")
                            if server_process.poll() is not None:
                                logger.error(f"llama-server exited with code: {server_process.returncode}")
                            break # Break from inner run loop if server fails to start

                    except FileNotFoundError:
                        logger.error("llama-server executable not found. Please ensure it's in your PATH.")
                        break # Break from outer batch loop
                    except Exception as e:
                        logger.error(f"An unexpected error occurred during llama-server or client execution: {e}")
                        if server_process and server_process.stderr:
                            server_stderr = server_process.stderr.read()
                            logger.error(f"llama-server stderr during error: {server_stderr}")
                        break # Break from inner run loop
                    finally:
                        if server_process and server_process.poll() is None:
                            logger.info("Terminating llama-server process.")
                            server_process.terminate()
                            server_process.wait(timeout=10)
                            if server_process.poll() is None:
                                logger.warning("llama-server did not terminate gracefully. Killing it.")
                                server_process.kill()
                    
                    if server_process and server_process.poll() is not None:
                        break

                if not server_startup_successful:
                    break # Skip to next batch size if server didn't start

                num_clients_to_spawn = best_parallel_level * 2 # Saturating load
                try:
                    total_throughput = asyncio.run(self._simulate_concurrent_clients(num_clients_to_spawn, test_prompt, max_tokens_to_generate))
                    individual_run_results_batch.append({"generation_speed_tps": total_throughput})
                    all_throughput_speeds_for_current_batch_size.append(total_throughput)

                    if progress_callback:
                        progress_callback("Phase 2/2 Throughput (Stage 2)", step_description, current_step_count_stage2, total_steps_stage2, total_throughput, current_run_count_batch, 10)

                    # Check for stability after a minimum of 3 runs
                    if current_run_count_batch >= 3:
                        mean_throughput = statistics.mean(all_throughput_speeds_for_current_batch_size)
                        if len(all_throughput_speeds_for_current_batch_size) > 1:
                            std_dev_throughput = statistics.stdev(all_throughput_speeds_for_current_batch_size)
                        else:
                            std_dev_throughput = 0.0

                        if mean_throughput > 0 and (std_dev_throughput / mean_throughput) <= 0.05:
                            is_stable_batch = True
                            logger.info(f"Results stable for --batch-size {batch_size} after {current_run_count_batch} runs (StdDev/Mean: {std_dev_throughput/mean_throughput:.4f})")
                            break # Stability achieved, break inner loop
                        else:
                            logger.info(f"Results unstable for --batch-size {batch_size} after {current_run_count_batch} runs (StdDev/Mean: {std_dev_throughput/mean_throughput:.4f}). Continuing...")

                except Exception as e:
                    logger.error(f"Client simulation failed for batch_size={batch_size} (Run {current_run_count_batch}/10): {e}")
                    break  # Skip to next batch size

            # After inner while loop (either stable or max runs reached)
            if len(all_throughput_speeds_for_current_batch_size) > 0:
                mean_throughput_batch = statistics.mean(all_throughput_speeds_for_current_batch_size)
                throughput_std_batch = statistics.stdev(all_throughput_speeds_for_current_batch_size) if len(all_throughput_speeds_for_current_batch_size) > 1 else 0.0

                notes_batch = []
                if high_variance_warning_batch:
                    notes_batch.append("High Variance Warning: Throughput results did not stabilize after 10 runs for this batch size.")

                benchmark_results.append(BenchmarkResult(
                    n_gpu_layers=n_gpu_layers,
                    prompt_speed_tps=0.0,
                    generation_speed_tps=mean_throughput_batch,
                    batch_size=batch_size,
                    parallel_level=best_parallel_level,
                    generation_speed_tps_mean=mean_throughput_batch,
                    generation_speed_tps_std=throughput_std_batch,
                    individual_results=individual_run_results_batch,
                    notes=notes_batch if notes_batch else None
                ))
                logger.info(f"Result for batch_size={batch_size}, parallel={best_parallel_level}: Mean Throughput={mean_throughput_batch:.2f} t/s, StdDev={throughput_std_batch:.2f}")
            else:
                logger.warning(f"No successful runs for --batch-size {batch_size}. Skipping to next batch size.")

        return self._analyze_throughput_results(benchmark_results, n_gpu_layers)