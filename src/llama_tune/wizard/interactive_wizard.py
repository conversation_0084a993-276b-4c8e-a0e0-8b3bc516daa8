import typer
import questionary
from typing import Dict, Any, Optional

from llama_tune.core.sampling_presets import SAMPLING_PRESETS
from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile, run_feasibility_check, FeasibilityError
from llama_tune.core.data_models import OptimalConfiguration, BenchmarkResult, ModelProfile, SystemProfile

class InteractiveWizard:
    def __init__(self):
        self.state: Dict[str, Any] = {}

    def start(self):
        typer.echo("Welcome to the Llama-Tune Interactive Setup Wizard!")
        typer.echo("This wizard will guide you through configuring optimal settings for your GGUF model.")
        typer.echo("Let's get started!")
        self._select_use_case()
        self._prompt_context_size()
        self._gather_system_and_model_info()

    def _select_use_case(self):
        use_cases = list(SAMPLING_PRESETS.keys())
        selected_use_case = questionary.select(
            "Please select your intended use case:",
            choices=use_cases
        ).ask()

        if selected_use_case:
            typer.echo(f"You selected: {selected_use_case}")
            self.state["selected_use_case"] = selected_use_case
            self.state["sampling_parameters"] = SAMPLING_PRESETS[selected_use_case]
            self.state["prioritize_prompt_speed"] = (selected_use_case == "RAG / Document Analysis")
        else:
            typer.echo("No use case selected. Exiting wizard.")
            raise typer.Exit()

    def _prompt_context_size(self):
        while True:
            try:
                ctx_size_input = questionary.text(
                    "Enter desired context size (--ctx-size) [default: 2048]:",
                    default="2048"
                ).ask()

                if ctx_size_input is None:
                    typer.echo("No context size entered. Exiting wizard.")
                    raise typer.Exit()

                ctx_size = int(ctx_size_input)
                if ctx_size <= 0:
                    typer.echo("Context size must be a positive integer. Please try again.")
                else:
                    self.state["ctx_size"] = ctx_size
                    typer.echo(f"Context size set to: {ctx_size}")
                    break
            except ValueError:
                typer.echo("Invalid input. Please enter a positive integer for context size.")

    def _gather_system_and_model_info(self):
        typer.echo("\nGathering system and model information...")
        
        # 1. Detect System Profile
        system_profile = get_system_profile()
        self.state["system_profile"] = system_profile
        typer.echo(f"Detected System Profile: CPU Cores={system_profile.cpu_cores}, RAM={system_profile.total_ram_gb}GB")

        # 2. Get Model Profile (prompt for model path)
        while True:
            model_path = questionary.text(
                "Enter path to your GGUF model file:",
                validate=lambda text: "Model path cannot be empty." if not text else True
            ).ask()

            if model_path is None:
                typer.echo("No model path entered. Exiting wizard.")
                raise typer.Exit()
            
            try:
                model_profile = get_model_profile(model_path)
                self.state["model_profile"] = model_profile
                typer.echo(f"Detected Model Profile: Architecture={model_profile.architecture}, Layers={model_profile.layer_count}")
                break
            except FeasibilityError as e:
                typer.echo(f"Error: {e}. Please provide a valid GGUF model path.")
            except Exception as e:
                typer.echo(f"An unexpected error occurred while processing the model: {e}. Please try again.")

        # 3. Perform Pre-flight Feasibility Check
        try:
            run_feasibility_check(self.state["model_profile"], self.state["system_profile"])
            typer.echo("Feasibility check passed.")
        except FeasibilityError as e:
            typer.echo(f"Feasibility Check Failed: {e}. The recommended command might not run optimally or at all.")
            # Do not exit, but inform the user.
        
        # Placeholder for benchmarking results (will be populated in future stories)
        # For now, create a dummy BenchmarkResult
        dummy_benchmark_result = BenchmarkResult(
            n_gpu_layers=0,
            prompt_speed_tps=0.0,
            generation_speed_tps=0.0,
            batch_size=None,
            parallel_level=None
        )
        self.state["best_benchmark_result"] = dummy_benchmark_result

    def get_optimal_configuration(self) -> Optional[OptimalConfiguration]:
        if "system_profile" not in self.state or \
           "model_profile" not in self.state or \
           "best_benchmark_result" not in self.state or \
           "ctx_size" not in self.state:
            typer.echo("Warning: Not all required information is available to generate optimal configuration.")
            return None

        system_profile: SystemProfile = self.state["system_profile"]
        model_profile: ModelProfile = self.state["model_profile"]

        notes = []
        if system_profile.cpu_cores == 0:
            notes.append("Could not detect physical CPU cores. The --threads argument was omitted from the command.")

        return OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=self.state["best_benchmark_result"],
            generated_command="",  # Empty - OutputGenerator will build the complete command
            notes=notes,
            ctx_size=self.state["ctx_size"],
            sampling_parameters=self.state.get("sampling_parameters", {})
        )