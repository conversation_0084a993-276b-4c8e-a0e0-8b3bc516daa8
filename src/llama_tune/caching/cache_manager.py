"""
Cache manager for storing and retrieving benchmark results.

This module implements caching functionality to store OptimalConfiguration objects
and retrieve them based on a deterministic cache key derived from hardware specs
and model file characteristics.
"""

import json
import hashlib
import logging
import os
import platform
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import asdict

from ..core.data_models import OptimalConfiguration, SystemProfile, ModelProfile, BenchmarkResult, GpuInfo

logger = logging.getLogger(__name__)


class CacheManager:
    """
    Manages caching of benchmark results to avoid re-running expensive benchmarks.
    
    The cache is stored in the user's standard cache directory (~/.cache/llama-tune)
    and uses a deterministic key based on hardware specs and model file hash.
    """
    
    def __init__(self, cache_dir: Optional[Path] = None):
        """
        Initialize the cache manager.
        
        Args:
            cache_dir: Optional custom cache directory. If None, uses ~/.cache/llama-tune
        """
        if cache_dir is None:
            # Use standard user cache directory
            if platform.system() == "Windows":
                cache_base = Path.home() / "AppData" / "Local"
            elif platform.system() == "Darwin":  # macOS
                cache_base = Path.home() / "Library" / "Caches"
            else:  # Linux and other Unix-like systems
                cache_base = Path.home() / ".cache"
            
            self.cache_dir = cache_base / "llama-tune"
        else:
            self.cache_dir = cache_dir
        
        # Ensure cache directory exists
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Cache directory: {self.cache_dir}")
    
    def generate_cache_key(self, system_profile: SystemProfile, model_profile: ModelProfile, 
                          ctx_size: int, use_case: str, max_vram_gb: Optional[float] = None) -> str:
        """
        Generate a deterministic cache key based on hardware specs and model characteristics.
        
        Args:
            system_profile: System hardware profile
            model_profile: Model file profile
            ctx_size: Context size used for benchmarking
            use_case: Use case for benchmarking
            max_vram_gb: Maximum VRAM constraint if specified
            
        Returns:
            A deterministic cache key string
        """
        # Create a dictionary with all relevant cache key components
        key_components = {
            # System specs that affect performance
            "cpu_cores": system_profile.cpu_cores,
            "total_ram_gb": system_profile.total_ram_gb,
            "numa_detected": system_profile.numa_detected,
            "blas_backend": system_profile.blas_backend,
            # GPU specs - sort by model name for deterministic ordering
            "gpus": sorted([{"model_name": gpu.model_name, "vram_gb": gpu.vram_gb} 
                           for gpu in system_profile.gpus], key=lambda x: x["model_name"]),
            # Model characteristics
            "model_architecture": model_profile.architecture,
            "model_layer_count": model_profile.layer_count,
            "model_quantization": model_profile.quantization_type,
            # Benchmark parameters
            "ctx_size": ctx_size,
            "use_case": use_case,
            "max_vram_gb": max_vram_gb,
            # Tool version to invalidate cache on updates
            "tool_version": self._get_tool_version()
        }
        
        # Add model file hash for uniqueness
        model_file_hash = self._get_file_hash(model_profile.file_path)
        key_components["model_file_hash"] = model_file_hash
        
        # Create deterministic JSON representation
        key_json = json.dumps(key_components, sort_keys=True, separators=(',', ':'))
        
        # Generate SHA256 hash of the key components
        cache_key = hashlib.sha256(key_json.encode('utf-8')).hexdigest()
        
        logger.debug(f"Generated cache key: {cache_key}")
        logger.debug(f"Key components: {key_components}")
        
        return cache_key
    
    def get_from_cache(self, cache_key: str) -> Optional[OptimalConfiguration]:
        """
        Retrieve a cached OptimalConfiguration by key.
        
        Args:
            cache_key: The cache key to look up
            
        Returns:
            OptimalConfiguration if found, None otherwise
        """
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if not cache_file.exists():
            logger.debug(f"Cache miss: {cache_key}")
            return None
        
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # Reconstruct OptimalConfiguration from cached data
            optimal_config = self._deserialize_optimal_configuration(cache_data)
            logger.info(f"Cache hit: {cache_key}")
            return optimal_config
            
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            logger.warning(f"Failed to load cache file {cache_file}: {e}")
            # Remove corrupted cache file
            try:
                cache_file.unlink()
                logger.debug(f"Removed corrupted cache file: {cache_file}")
            except OSError:
                pass
            return None
    
    def save_to_cache(self, cache_key: str, optimal_config: OptimalConfiguration) -> None:
        """
        Save an OptimalConfiguration to cache.
        
        Args:
            cache_key: The cache key to store under
            optimal_config: The configuration to cache
        """
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        try:
            # Serialize OptimalConfiguration to JSON-compatible format
            cache_data = self._serialize_optimal_configuration(optimal_config)
            
            # Write to temporary file first, then rename for atomic operation
            temp_file = cache_file.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, separators=(',', ': '))
            
            # Atomic rename
            temp_file.rename(cache_file)
            logger.info(f"Saved to cache: {cache_key}")
            
        except (OSError, TypeError) as e:
            logger.error(f"Failed to save cache file {cache_file}: {e}")
            # Clean up temporary file if it exists
            temp_file = cache_file.with_suffix('.tmp')
            if temp_file.exists():
                try:
                    temp_file.unlink()
                except OSError:
                    pass
    
    def _get_file_hash(self, file_path: str) -> str:
        """
        Calculate SHA256 hash of a file.
        
        Args:
            file_path: Path to the file to hash
            
        Returns:
            SHA256 hash as hex string
        """
        try:
            hasher = hashlib.sha256()
            with open(file_path, 'rb') as f:
                # Read file in chunks to handle large files efficiently
                for chunk in iter(lambda: f.read(8192), b""):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except OSError as e:
            logger.warning(f"Failed to hash file {file_path}: {e}")
            # Return a fallback hash based on file path and size
            try:
                stat = os.stat(file_path)
                fallback_data = f"{file_path}:{stat.st_size}:{stat.st_mtime}"
                return hashlib.sha256(fallback_data.encode('utf-8')).hexdigest()
            except OSError:
                # Last resort: hash just the file path
                return hashlib.sha256(file_path.encode('utf-8')).hexdigest()
    
    def _get_tool_version(self) -> str:
        """
        Get the tool version for cache invalidation.
        
        Returns:
            Tool version string
        """
        # For now, return a simple version. In a real implementation,
        # this could read from package metadata or a version file.
        return "1.0.0"
    
    def _serialize_optimal_configuration(self, config: OptimalConfiguration) -> Dict[str, Any]:
        """
        Convert OptimalConfiguration to JSON-serializable format.
        
        Args:
            config: OptimalConfiguration to serialize
            
        Returns:
            Dictionary representation suitable for JSON serialization
        """
        return asdict(config)
    
    def _deserialize_optimal_configuration(self, data: Dict[str, Any]) -> OptimalConfiguration:
        """
        Convert JSON data back to OptimalConfiguration.
        
        Args:
            data: Dictionary from JSON deserialization
            
        Returns:
            OptimalConfiguration object
        """
        # Reconstruct nested objects
        gpu_infos = [GpuInfo(**gpu) for gpu in data['system_profile']['gpus']]
        system_profile = SystemProfile(
            cpu_cores=data['system_profile']['cpu_cores'],
            total_ram_gb=data['system_profile']['total_ram_gb'],
            gpus=gpu_infos,
            numa_detected=data['system_profile']['numa_detected'],
            blas_backend=data['system_profile']['blas_backend']
        )
        
        model_profile = ModelProfile(**data['model_profile'])
        
        benchmark_result = BenchmarkResult(**data['best_benchmark_result'])
        
        return OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=benchmark_result,
            generated_command=data['generated_command'],
            notes=data['notes'],
            ctx_size=data.get('ctx_size'),
            sampling_parameters=data.get('sampling_parameters'),
            use_case=data.get('use_case'),
            max_vram_gb=data.get('max_vram_gb')
        )
